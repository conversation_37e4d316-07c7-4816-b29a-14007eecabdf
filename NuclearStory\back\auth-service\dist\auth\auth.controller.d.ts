import { AuthService, AuthResponse } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { User } from '../users/entities/user.entity';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<AuthResponse>;
    login(loginDto: LoginDto): Promise<AuthResponse>;
    getProfile(user: User): Promise<{
        id: string;
        email: string;
        username: string;
        role: import("../users/entities/user.entity").UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
    }>;
    verifyToken(user: User): Promise<{
        valid: boolean;
        user: {
            id: string;
            email: string;
            username: string;
            role: import("../users/entities/user.entity").UserRole;
        };
    }>;
}
