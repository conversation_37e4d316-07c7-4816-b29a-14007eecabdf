import { EventType } from '../enums';
export interface EventChoice {
    id: string;
    text: string;
    description?: string;
    requirements?: {
        skills?: Record<string, number>;
        items?: string[];
        reputation?: Record<string, number>;
        stats?: Record<string, number>;
        questCompleted?: string[];
        level?: number;
    };
    consequences: {
        addItems?: string[];
        removeItems?: string[];
        healthChange?: number;
        radiationChange?: number;
        statChanges?: Record<string, number>;
        reputationChanges?: Record<string, number>;
        startQuest?: string;
        completeQuest?: string;
        failQuest?: string;
        teleportTo?: string;
        unlockLocation?: string;
        spawnNPCs?: string[];
        killNPCs?: string[];
        currencyChange?: number;
        experienceGain?: number;
        skillExperience?: Record<string, number>;
        triggerEvent?: string;
        preventEvent?: string;
        resultText?: string;
        narrativeFlag?: string;
    };
    successChance?: number;
    skillCheck?: {
        skill: string;
        difficulty: number;
        criticalSuccess?: number;
        criticalFailure?: number;
    };
}
export interface EventCondition {
    type: 'time' | 'location' | 'quest' | 'item' | 'reputation' | 'level' | 'random' | 'flag';
    timeOfDay?: 'day' | 'night' | 'dawn' | 'dusk';
    dayOfWeek?: number;
    gameTime?: {
        after?: Date;
        before?: Date;
    };
    locationId?: string;
    locationType?: string;
    questActive?: string;
    questCompleted?: string;
    questFailed?: string;
    hasItem?: string;
    itemQuantity?: number;
    factionReputation?: {
        factionId: string;
        minimum: number;
        maximum?: number;
    };
    playerLevel?: {
        minimum?: number;
        maximum?: number;
    };
    playerStats?: Record<string, number>;
    probability?: number;
    narrativeFlag?: {
        flag: string;
        value: boolean;
    };
}
export interface Event {
    id: string;
    name: string;
    description: string;
    type: EventType;
    title: string;
    text: string;
    flavorText?: string;
    choices: EventChoice[];
    hasChoices: boolean;
    autoResolve: boolean;
    triggerConditions: EventCondition[];
    isRepeatable: boolean;
    cooldownTime?: number;
    maxOccurrences?: number;
    currentOccurrences: number;
    priority: number;
    duration?: number;
    locationId?: string;
    radius?: number;
    requirements?: {
        playerLevel?: number;
        questsCompleted?: string[];
        itemsRequired?: string[];
        factionsRequired?: Record<string, number>;
    };
    image?: string;
    sound?: string;
    music?: string;
    storyWeight: number;
    consequenceLevel: 'minor' | 'moderate' | 'major' | 'critical';
    tags: string[];
    category: string;
    isTriggered: boolean;
    isCompleted: boolean;
    isActive: boolean;
    playerChoices: string[];
    lastTriggeredAt?: Date;
    firstTriggeredAt?: Date;
    createdAt: Date;
    lastUpdatedAt: Date;
}
