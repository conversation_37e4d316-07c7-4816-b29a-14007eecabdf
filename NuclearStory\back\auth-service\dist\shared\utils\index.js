"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Constants = exports.DateUtils = exports.GameUtils = exports.ValidationUtils = void 0;
class ValidationUtils {
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    static isValidUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
        return usernameRegex.test(username);
    }
    static isValidPassword(password) {
        return password.length >= 6;
    }
}
exports.ValidationUtils = ValidationUtils;
class GameUtils {
    static clampStat(value, min = 0, max = 100) {
        return Math.max(min, Math.min(max, value));
    }
    static applyStatChanges(current, changes) {
        return {
            health: this.clampStat((current.health || 100) + (changes.health || 0)),
            maxHealth: current.maxHealth || 100,
            energy: this.clampStat((current.energy || 100) + (changes.energy || 0)),
            maxEnergy: current.maxEnergy || 100,
            hunger: this.clampStat((current.hunger || 100) + (changes.hunger || 0)),
            thirst: this.clampStat((current.thirst || 100) + (changes.thirst || 0)),
            radiation: this.clampStat((current.radiation || 0) + (changes.radiation || 0)),
            sanity: current.sanity !== undefined ?
                this.clampStat((current.sanity || 100) + (changes.sanity || 0)) :
                undefined,
        };
    }
    static calculateSurvivalScore(stats) {
        const weights = {
            health: 0.3,
            hunger: 0.2,
            thirst: 0.2,
            energy: 0.15,
            radiation: -0.15,
        };
        return Math.round((stats.health * weights.health) +
            (stats.hunger * weights.hunger) +
            (stats.thirst * weights.thirst) +
            (stats.energy * weights.energy) +
            (stats.radiation * weights.radiation));
    }
    static isPlayerInDanger(stats) {
        return stats.health <= 20 ||
            stats.hunger <= 10 ||
            stats.thirst <= 10 ||
            stats.radiation >= 80;
    }
}
exports.GameUtils = GameUtils;
class DateUtils {
    static formatPlaytime(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }
    static getTimeSince(date) {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays > 0) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        }
        if (diffHours > 0) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        }
        if (diffMins > 0) {
            return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
        }
        return 'Just now';
    }
}
exports.DateUtils = DateUtils;
class Constants {
}
exports.Constants = Constants;
Constants.DEFAULT_GAME_STATS = {
    health: 100,
    maxHealth: 100,
    energy: 100,
    maxEnergy: 100,
    hunger: 100,
    thirst: 100,
    radiation: 0,
};
Constants.MAX_INVENTORY_SLOTS = 50;
Constants.MAX_SAVE_SLOTS = 10;
Constants.DEFAULT_LOCATION = 'bunker';
Constants.STAT_DECAY_RATES = {
    hunger: 1,
    thirst: 1.5,
    energy: 0.5,
};
Constants.CRITICAL_THRESHOLDS = {
    health: 20,
    hunger: 10,
    thirst: 10,
    radiation: 80,
    energy: 10,
};
__exportStar(require("./diceRoll"), exports);
__exportStar(require("./generateId"), exports);
//# sourceMappingURL=index.js.map