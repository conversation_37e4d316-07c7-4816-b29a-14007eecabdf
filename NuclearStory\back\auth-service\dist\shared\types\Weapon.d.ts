import { ItemType, ItemRarity } from '../enums';
export interface WeaponDamage {
    min: number;
    max: number;
    type: 'normal' | 'laser' | 'plasma' | 'explosive' | 'fire' | 'electric' | 'poison';
}
export interface WeaponStats {
    damage: WeaponDamage;
    accuracy: number;
    criticalChance: number;
    criticalMultiplier: number;
    range: number;
    actionPointCost: number;
    usesAmmo: boolean;
    ammoType?: string;
    magazineSize?: number;
    currentAmmo?: number;
    reloadAPCost?: number;
    durability: number;
    maxDurability: number;
    degradationRate: number;
    armorPenetration: number;
    burstFire: boolean;
    burstSize?: number;
    fullAuto: boolean;
    SParamRequired: number;
    PParamRequired: number;
    EParamRequired: number;
    IParamRequired: number;
    AParamRequired: number;
}
export interface WeaponModification {
    id: string;
    name: string;
    description: string;
    slot: 'barrel' | 'scope' | 'stock' | 'magazine' | 'trigger' | 'grip';
    effects: {
        damageBonus?: number;
        accuracyBonus?: number;
        rangeBonus?: number;
        criticalBonus?: number;
        apCostReduction?: number;
        durabilityBonus?: number;
    };
    requirements?: {
        skill?: Record<string, number>;
        items?: string[];
    };
    isInstalled: boolean;
}
export interface Weapon {
    id: string;
    name: string;
    description: string;
    type: ItemType;
    rarity: ItemRarity;
    weight: number;
    value: number;
    weaponRange: 'melee' | 'ranged' | 'mixed';
    weaponType: 'melee' | 'light ballistic' | 'heavy ballistic' | 'energy' | 'explosive' | 'thrown' | 'exotic';
    weaponClass: 'unarmed' | 'blade' | 'club' | 'pistol' | 'rifle' | 'shotgun' | 'smg' | 'lmg' | 'sniper' | 'launcher';
    stats: WeaponStats;
    attackModel: 'single' | 'burst' | 'full-auto';
    modificationSlots: string[];
    installedMods: WeaponModification[];
    maxMods: number;
    sprite?: string;
    icon?: string;
    soundEffects?: {
        fire?: string;
        reload?: string;
        empty?: string;
    };
    specialAbilities: string[];
    canBeCrafted: boolean;
    craftingRecipe?: {
        components: Record<string, number>;
        skill: Record<string, number>;
        tools?: string[];
    };
    canBeRepaired: boolean;
    repairCost?: Record<string, number>;
    manufacturer?: string;
    yearMade?: number;
    loreText?: string;
    isUnique: boolean;
    isQuestItem: boolean;
    canBeSold: boolean;
    canBeDropped: boolean;
    canBeStolen: boolean;
    createdAt: Date;
    lastUsedAt?: Date;
    lastRepairedAt?: Date;
}
