{"version": 3, "file": "diceRoll.js", "sourceRoot": "", "sources": ["../../../src/shared/utils/diceRoll.ts"], "names": [], "mappings": ";;AAcA,0BAEC;AAKD,4BAMC;AAKD,4CA2BC;AAKD,kBAEC;AAKD,oBAEC;AAKD,gCAGC;AAKD,gCAQC;AAKD,wCAMC;AAKD,4BAIC;AAKD,sCAGC;AAKD,0CAIC;AAKD,wCAUC;AAKD,4BAuBC;AAhKD,SAAgB,OAAO,CAAC,KAAa;IACnC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9C,CAAC;AAKD,SAAgB,QAAQ,CAAC,KAAa,EAAE,KAAa;IACnD,MAAM,KAAK,GAAa,EAAE,CAAA;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;IAC5B,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAKD,SAAgB,gBAAgB,CAC9B,KAAa,EACb,KAAa,EACb,WAAmB,CAAC,EACpB,UAAmB;IAEnB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAA;IACzD,MAAM,KAAK,GAAG,MAAM,GAAG,QAAQ,CAAA;IAG/B,MAAM,iBAAiB,GAAG,KAAK,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAA;IACzE,MAAM,iBAAiB,GAAG,KAAK,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAA;IAGxE,MAAM,SAAS,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAA;IAExE,OAAO;QACL,MAAM;QACN,KAAK;QACL,QAAQ;QACR,KAAK;QACL,iBAAiB;QACjB,iBAAiB;QACjB,SAAS;QACT,UAAU;KACX,CAAA;AACH,CAAC;AAKD,SAAgB,GAAG,CAAC,WAAmB,CAAC,EAAE,UAAmB;IAC3D,OAAO,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;AACtD,CAAC;AAKD,SAAgB,IAAI,CAAC,WAAmB,CAAC,EAAE,UAAmB;IAC5D,OAAO,gBAAgB,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;AACvD,CAAC;AAKD,SAAgB,UAAU,CAAC,KAAa,EAAE,KAAa,EAAE,WAAmB,CAAC;IAC3E,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;IACvD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;AAClC,CAAC;AAKD,SAAgB,UAAU,CACxB,UAAkB,EAClB,SAAiB,EACjB,UAAkB,EAClB,WAAmB,CAAC;IAEpB,MAAM,aAAa,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAA;IACvD,OAAO,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;AACvC,CAAC;AAKD,SAAgB,cAAc,CAC5B,SAAiB,EACjB,UAAkB,EAClB,WAAmB,CAAC;IAEpB,OAAO,GAAG,CAAC,SAAS,GAAG,QAAQ,EAAE,UAAU,CAAC,CAAA;AAC9C,CAAC;AAKD,SAAgB,QAAQ,CAAC,SAAiB,EAAE,aAAqB,EAAE;IACjE,MAAM,IAAI,GAAG,IAAI,EAAE,CAAA;IACnB,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;IACvD,OAAO,IAAI,CAAC,KAAK,IAAI,cAAc,CAAA;AACrC,CAAC;AAKD,SAAgB,aAAa,CAAC,eAAuB;IACnD,MAAM,IAAI,GAAG,IAAI,EAAE,CAAA;IACnB,OAAO,IAAI,CAAC,KAAK,IAAI,eAAe,CAAA;AACtC,CAAC;AAKD,SAAgB,eAAe,CAAC,cAAsB,EAAE,eAAuB,CAAC;IAC9E,MAAM,cAAc,GAAG,cAAc,GAAG,YAAY,CAAA;IACpD,MAAM,IAAI,GAAG,IAAI,EAAE,CAAA;IACnB,OAAO,IAAI,CAAC,KAAK,IAAI,cAAc,CAAA;AACrC,CAAC;AAKD,SAAgB,cAAc,CAAC,iBAAyB,EAAE,aAAqB;IAC7E,MAAM,gBAAgB,GAAG,CAAC,iBAAiB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAA;IAClE,MAAM,IAAI,GAAG,IAAI,EAAE,CAAA;IAGnB,IAAI,IAAI,CAAC,KAAK,GAAG,gBAAgB,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IACpD,CAAC;IAED,OAAO,iBAAiB,CAAA;AAC1B,CAAC;AAKD,SAAgB,QAAQ,CAAC,UAAkB,EAAE,kBAA0B,CAAC;IAItE,MAAM,IAAI,GAAG,IAAI,EAAE,CAAA;IACnB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,UAAU,CAAA;IAExC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAA;IAC5C,CAAC;IAGD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,CAAA;IACzC,IAAI,OAAuE,CAAA;IAE3E,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,WAAW,CAAA;SAC7C,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,MAAM,CAAA;SAC7C,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,MAAM,CAAA;SAC7C,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,UAAU,CAAA;SACjD,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,GAAG,QAAQ,CAAA;;QAC/C,OAAO,GAAG,MAAM,CAAA;IAErB,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAA;AAC7B,CAAC"}