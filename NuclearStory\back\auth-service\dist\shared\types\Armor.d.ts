import { ItemType, ItemRarity } from '../enums';
export interface ArmorProtection {
    damageThreshold: number;
    damageResistance: number;
    resistances: {
        normal: number;
        laser: number;
        plasma: number;
        explosive: number;
        fire: number;
        electric: number;
        poison: number;
        radiation: number;
        acid: number;
        cold: number;
    };
    environmentalProtection: {
        radiation: number;
        temperature: number;
        pressure: number;
        vacuum: boolean;
        underwater: boolean;
        chemical: number;
    };
}
export interface ArmorStats {
    protection: ArmorProtection;
    weight: number;
    encumbrance: number;
    durability: number;
    maxDurability: number;
    degradationRate: number;
    statModifiers: {
        strengthBonus?: number;
        perceptionPenalty?: number;
        enduranceBonus?: number;
        charismaModifier?: number;
        intelligencePenalty?: number;
        agilityPenalty?: number;
        luckModifier?: number;
    };
    skillModifiers: {
        sneakPenalty?: number;
        lockpickPenalty?: number;
        repairBonus?: number;
        scienceBonus?: number;
        medicineBonus?: number;
        speechModifier?: number;
    };
    requirements: {
        strengthRequired: number;
        enduranceRequired: number;
        skillRequired?: Record<string, number>;
    };
    specialProperties: {
        stealthDetection: boolean;
        nightVision: boolean;
        thermalVision: boolean;
        breathingApparatus: boolean;
        communicationSystem: boolean;
        targetingSystem: boolean;
        jumpJets: boolean;
        magneticBoots: boolean;
    };
}
export interface ArmorModification {
    id: string;
    name: string;
    description: string;
    slot: 'plating' | 'lining' | 'servos' | 'helmet_mod' | 'torso_mod' | 'limb_mod' | 'power_system';
    effects: {
        damageResistanceBonus?: number;
        damageThresholdBonus?: number;
        radiationResistanceBonus?: number;
        strengthBonus?: number;
        agilityBonus?: number;
        perceptionBonus?: number;
        weightReduction?: number;
        encumbranceReduction?: number;
        durabilityBonus?: number;
        addNightVision?: boolean;
        addThermalVision?: boolean;
        addStealthField?: boolean;
        addShielding?: boolean;
    };
    requirements?: {
        skill?: Record<string, number>;
        items?: string[];
        powerRequired?: number;
    };
    isInstalled: boolean;
    powerConsumption?: number;
}
export interface Armor {
    id: string;
    name: string;
    description: string;
    type: ItemType;
    rarity: ItemRarity;
    weight: number;
    value: number;
    armorType: 'clothing' | 'light' | 'medium' | 'heavy' | 'power_armor' | 'environmental_suit';
    armorClass: 'jumpsuit' | 'leather' | 'metal' | 'combat' | 'riot' | 'military' | 'power' | 'hazmat' | 'space_suit';
    equipmentSlots: ('head' | 'torso' | 'arms' | 'legs' | 'feet' | 'full_body')[];
    stats: ArmorStats;
    powerSystem?: {
        hasPowerSystem: boolean;
        maxPower: number;
        currentPower: number;
        powerConsumption: number;
        powerSources: string[];
        batteryLife: number;
    };
    modificationSlots: string[];
    installedMods: ArmorModification[];
    maxMods: number;
    sprite?: string;
    icon?: string;
    soundEffects?: {
        equip?: string;
        unequip?: string;
        move?: string;
        damage?: string;
    };
    specialAbilities: string[];
    canBeCrafted: boolean;
    craftingRecipe?: {
        components: Record<string, number>;
        skill: Record<string, number>;
        tools?: string[];
        workbench?: string;
    };
    canBeRepaired: boolean;
    repairCost?: Record<string, number>;
    repairSkill?: string;
    setInfo?: {
        setId: string;
        setName: string;
        setPieces: string[];
        setBonuses: Array<{
            piecesRequired: number;
            bonus: {
                statBonus?: Record<string, number>;
                resistanceBonus?: Record<string, number>;
                specialAbility?: string;
            };
        }>;
    };
    environmentalEffects: {
        heatSignature: number;
        noiseLevel: number;
        visibility: number;
        weatherProtection: boolean;
        temperatureRange: {
            min: number;
            max: number;
        };
    };
    condition: {
        currentCondition: number;
        maintenanceRequired: boolean;
        lastMaintenanceAt?: Date;
        maintenanceInterval: number;
    };
    manufacturer?: string;
    model?: string;
    yearMade?: number;
    militaryDesignation?: string;
    loreText?: string;
    isUnique: boolean;
    isQuestItem: boolean;
    canBeSold: boolean;
    canBeDropped: boolean;
    canBeStolen: boolean;
    requiresTraining: boolean;
    isRestricted: boolean;
    createdAt: Date;
    lastUsedAt?: Date;
    lastRepairedAt?: Date;
    lastMaintenanceAt?: Date;
}
