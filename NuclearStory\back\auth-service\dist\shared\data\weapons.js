"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WEAPONS = void 0;
const enums_1 = require("../enums");
exports.WEAPONS = [
    {
        id: '10mm-pistol',
        name: '10мм пистолет',
        description: 'Надежный полуавтоматический пистолет довоенного производства. Стандартное оружие сил безопасности Убежищ.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 1.5,
        value: 150,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'pistol',
        stats: {
            damage: { min: 8, max: 15, type: 'normal' },
            accuracy: 75,
            criticalChance: 5,
            criticalMultiplier: 2.0,
            range: 8,
            actionPointCost: 3,
            usesAmmo: true,
            ammoType: '10mm',
            magazineSize: 12,
            currentAmmo: 12,
            reloadAPCost: 2,
            durability: 80,
            maxDurability: 100,
            degradationRate: 0.5,
            armorPenetration: 0,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 3,
            PParamRequired: 4,
            EParamRequired: 3,
            IParamRequired: 3,
            AParamRequired: 5,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'grip', 'magazine'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Colt Arms',
        yearMade: 2077,
        loreText: 'Этот пистолет был стандартным оружием довоенной полиции и сил безопасности.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'desert-eagle',
        name: 'Пустынный орел .44',
        description: 'Мощный пистолет крупного калибра. Один выстрел может остановить мутанта.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 2.2,
        value: 400,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'pistol',
        stats: {
            damage: { min: 18, max: 35, type: 'normal' },
            accuracy: 65,
            criticalChance: 12,
            criticalMultiplier: 2.5,
            range: 10,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: '.44_magnum',
            magazineSize: 6,
            currentAmmo: 6,
            reloadAPCost: 3,
            durability: 90,
            maxDurability: 120,
            degradationRate: 0.3,
            armorPenetration: 5,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 5,
            PParamRequired: 6,
            EParamRequired: 4,
            IParamRequired: 3,
            AParamRequired: 4,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'scope', 'grip'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Magnum Research',
        yearMade: 2076,
        loreText: 'Гражданский пистолет, но сильнее, чем кажется.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'weap_revolver_4570',
        name: 'Охотничий револьвер .45-70',
        description: 'Старая, но мощная игрушка для тех, кто умеет стрелять.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 4.2,
        value: 1800,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'pistol',
        stats: {
            damage: { min: 40, max: 70, type: 'normal' },
            accuracy: 70,
            criticalChance: 18,
            criticalMultiplier: 2.4,
            range: 6,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: '45-70',
            magazineSize: 6,
            currentAmmo: 6,
            reloadAPCost: 4,
            durability: 90,
            maxDurability: 100,
            degradationRate: 0.04,
            armorPenetration: 25,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 4,
            PParamRequired: 4,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 3,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'trigger'],
        installedMods: [],
        maxMods: 2,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Winchester',
        yearMade: 1892,
        loreText: '',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: "weap_sawedoff_doublebarrel",
        name: "Обрез двустволки",
        description: "Два громких аргумента, если переговорам не суждено состояться.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 4.5,
        value: 600,
        weaponRange: "ranged",
        weaponType: "light ballistic",
        weaponClass: "shotgun",
        stats: {
            damage: { min: 15, max: 30, type: "normal" },
            accuracy: 45,
            criticalChance: 10,
            criticalMultiplier: 2.0,
            range: 4,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: "12gauge",
            magazineSize: 2,
            currentAmmo: 2,
            reloadAPCost: 5,
            durability: 80,
            maxDurability: 80,
            degradationRate: 0.06,
            armorPenetration: 10,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 3,
            PParamRequired: 2,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 2
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: "doublebarrel",
        name: "Охотничий дробовик",
        description: "Ухоженая, явно хранилась в оружейном сейфе долгое время. ",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 7.5,
        value: 1100,
        weaponRange: "ranged",
        weaponType: "light ballistic",
        weaponClass: "shotgun",
        stats: {
            damage: { min: 22, max: 35, type: "normal" },
            accuracy: 65,
            criticalChance: 10,
            criticalMultiplier: 2.0,
            range: 8,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: "12gauge",
            magazineSize: 2,
            currentAmmo: 2,
            reloadAPCost: 5,
            durability: 80,
            maxDurability: 80,
            degradationRate: 0.06,
            armorPenetration: 10,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 4,
            PParamRequired: 3,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 2
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: 'combat-shotgun',
        name: 'Боевой дробовик',
        description: 'Полуавтоматический дробовик с барабанным магазином. Эффективен в ближнем бою.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 8.5,
        value: 1800,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'shotgun',
        stats: {
            damage: { min: 28, max: 50, type: 'normal' },
            accuracy: 55,
            criticalChance: 8,
            criticalMultiplier: 2.0,
            range: 6,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: '12_gauge',
            magazineSize: 12,
            currentAmmo: 12,
            reloadAPCost: 4,
            durability: 75,
            maxDurability: 100,
            degradationRate: 0.8,
            armorPenetration: 0,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 6,
            PParamRequired: 4,
            EParamRequired: 5,
            IParamRequired: 3,
            AParamRequired: 4,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'stock', 'magazine'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Winchester',
        yearMade: 2075,
        loreText: 'Популярное оружие среди рейдеров и караванщиков благодаря своей разрушительной силе.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'assault-rifle',
        name: 'Штурмовая винтовка',
        description: 'Стандартная штурмовая винтовка довоенного образца. Точность и мощь в одном.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 3.2,
        value: 800,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'rifle',
        stats: {
            damage: { min: 11, max: 21, type: 'normal' },
            accuracy: 75,
            criticalChance: 6,
            criticalMultiplier: 2.0,
            range: 15,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: '5.56mm',
            magazineSize: 24,
            currentAmmo: 24,
            reloadAPCost: 3,
            durability: 85,
            maxDurability: 110,
            degradationRate: 0.6,
            armorPenetration: 3,
            burstFire: true,
            burstSize: 3,
            fullAuto: true,
            SParamRequired: 5,
            PParamRequired: 5,
            EParamRequired: 5,
            IParamRequired: 4,
            AParamRequired: 5,
        },
        attackModel: 'full-auto',
        modificationSlots: ['barrel', 'scope', 'stock', 'magazine'],
        installedMods: [],
        maxMods: 4,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Remington',
        yearMade: 2077,
        loreText: 'Это оружие было основным в арсенале американской армии во время Великой войны.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'chinese-assault-rifle',
        name: 'Китайский автомат',
        description: 'Автоматическая винтовка китайского производства. Надежная и смертоносная.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 3.8,
        value: 1200,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'rifle',
        stats: {
            damage: { min: 15, max: 25, type: 'normal' },
            accuracy: 70,
            criticalChance: 6,
            criticalMultiplier: 2.0,
            range: 15,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: '5.56mm',
            magazineSize: 24,
            currentAmmo: 24,
            reloadAPCost: 3,
            durability: 85,
            maxDurability: 110,
            degradationRate: 0.6,
            armorPenetration: 3,
            burstFire: true,
            burstSize: 3,
            fullAuto: true,
            SParamRequired: 5,
            PParamRequired: 5,
            EParamRequired: 5,
            IParamRequired: 4,
            AParamRequired: 5,
        },
        attackModel: 'full-auto',
        modificationSlots: ['barrel', 'scope', 'stock', 'magazine'],
        installedMods: [],
        maxMods: 4,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        manufacturer: 'Norinco',
        yearMade: 2077,
        loreText: 'До Великой Войны каждый уважающий себя комми имел такую.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'weap_remington700',
        name: 'Remington',
        description: 'На третий день Бог создал Remington с продольно-скользящим затвором, чтобы человек мог стрелять в динозавров… и сохранять свободу.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 6.8,
        value: 2100,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'rifle',
        stats: {
            damage: { min: 45, max: 75, type: 'normal' },
            accuracy: 88,
            criticalChance: 18,
            criticalMultiplier: 2.4,
            range: 11,
            actionPointCost: 6,
            usesAmmo: true,
            ammoType: '308',
            magazineSize: 4,
            currentAmmo: 4,
            reloadAPCost: 5,
            durability: 100,
            maxDurability: 100,
            degradationRate: 0.025,
            armorPenetration: 25,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 5,
            PParamRequired: 6,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 4,
        },
        attackModel: 'single',
        modificationSlots: ['scope', 'barrel', 'stock'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'hunting-rifle',
        name: 'Охотничья винтовка',
        description: 'Болтовая винтовка для охоты на крупную дичь. Точная и мощная.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 4.2,
        value: 600,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'sniper',
        stats: {
            damage: { min: 30, max: 50, type: 'normal' },
            accuracy: 85,
            criticalChance: 15,
            criticalMultiplier: 3.0,
            range: 20,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: '.308',
            magazineSize: 5,
            currentAmmo: 5,
            reloadAPCost: 4,
            durability: 90,
            maxDurability: 120,
            degradationRate: 0.4,
            armorPenetration: 8,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 5,
            PParamRequired: 7,
            EParamRequired: 4,
            IParamRequired: 4,
            AParamRequired: 4,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'scope', 'stock'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'metal-parts': 8, wood: 4, screws: 6 },
            skill: { repair: 50, guns: 40 },
            tools: ['workbench'],
        },
        canBeRepaired: true,
        manufacturer: 'Remington',
        yearMade: 1902,
        loreText: 'Это моя винтовка. Таких винтовок много, но эта — моя. Моя винтовка — мой лучший друг. Она — моя жизнь.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'weap_sniper_308',
        name: 'Снайперская винтовка .308',
        description: 'Неумелый стрелок скорее всего сломает себе ключицу при выстреле',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 6.5,
        value: 1800,
        weaponRange: 'ranged',
        weaponType: 'light ballistic',
        weaponClass: 'sniper',
        stats: {
            damage: { min: 50, max: 85, type: 'normal' },
            accuracy: 90,
            criticalChance: 20,
            criticalMultiplier: 2.5,
            range: 20,
            actionPointCost: 6,
            usesAmmo: true,
            ammoType: '308',
            magazineSize: 5,
            currentAmmo: 5,
            reloadAPCost: 4,
            durability: 100,
            maxDurability: 100,
            degradationRate: 0.03,
            armorPenetration: 30,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 5,
            PParamRequired: 7,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 5,
        },
        attackModel: 'single',
        modificationSlots: ['scope', 'stock', 'barrel'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'weap_laser_pistol',
        name: 'Лазерный пистолет',
        description: 'Стандартное энергетическое оружие, точное и надёжное.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 3.5,
        value: 800,
        weaponRange: 'ranged',
        weaponType: 'energy',
        weaponClass: 'pistol',
        stats: {
            damage: { min: 20, max: 25, type: 'laser' },
            accuracy: 75,
            criticalChance: 10,
            criticalMultiplier: 2.0,
            range: 6,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: 'energy_cell',
            magazineSize: 15,
            currentAmmo: 15,
            reloadAPCost: 3,
            durability: 100,
            maxDurability: 100,
            degradationRate: 0.015,
            armorPenetration: 15,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 2,
            PParamRequired: 5,
            EParamRequired: 0,
            IParamRequired: 6,
            AParamRequired: 3,
        },
        attackModel: 'single',
        modificationSlots: ['scope', 'barrel'],
        installedMods: [],
        maxMods: 2,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'laser-rifle',
        name: 'Лазерная винтовка',
        description: 'Энергетическое оружие довоенного производства. Использует микроядерные батареи.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 5.2,
        value: 1500,
        weaponRange: 'ranged',
        weaponType: 'energy',
        weaponClass: 'rifle',
        stats: {
            damage: { min: 20, max: 35, type: 'laser' },
            accuracy: 80,
            criticalChance: 10,
            criticalMultiplier: 2.5,
            range: 15,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: 'energy_cell',
            magazineSize: 30,
            currentAmmo: 30,
            reloadAPCost: 3,
            durability: 70,
            maxDurability: 90,
            degradationRate: 1.2,
            armorPenetration: 12,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 4,
            PParamRequired: 6,
            EParamRequired: 5,
            IParamRequired: 6,
            AParamRequired: 5,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'scope', 'stock'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: ['armor_piercing'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { electronic_parts: 3, energy_cell: 2 },
        manufacturer: 'RobCo Industries',
        yearMade: 2077,
        loreText: 'Высокотехнологичное оружие, использовавшееся элитными подразделениями армии США.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'weap_plasma_pistol',
        name: 'Плазменный пистолет',
        description: 'Оружие будущего. Медленный, но разрушительный заряд.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 4.0,
        value: 1300,
        weaponRange: 'ranged',
        weaponType: 'energy',
        weaponClass: 'pistol',
        stats: {
            damage: { min: 25, max: 30, type: 'plasma' },
            accuracy: 80,
            criticalChance: 15,
            criticalMultiplier: 2.2,
            range: 12,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: 'plasma_cell',
            magazineSize: 10,
            currentAmmo: 10,
            reloadAPCost: 3,
            durability: 100,
            maxDurability: 100,
            degradationRate: 0.02,
            armorPenetration: 20,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 3,
            PParamRequired: 5,
            EParamRequired: 0,
            IParamRequired: 6,
            AParamRequired: 4,
        },
        attackModel: 'single',
        modificationSlots: ['grip', 'scope'],
        installedMods: [],
        maxMods: 2,
        specialAbilities: [],
        canBeCrafted: false,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'plasma-rifle',
        name: 'Плазменная винтовка',
        description: 'Экспериментальное энергетическое оружие. Стреляет сгустками плазмы.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.EPIC,
        weight: 4.0,
        value: 4500,
        weaponRange: 'ranged',
        weaponType: 'energy',
        weaponClass: 'rifle',
        stats: {
            damage: { min: 35, max: 60, type: 'plasma' },
            accuracy: 75,
            criticalChance: 8,
            criticalMultiplier: 3.0,
            range: 16,
            actionPointCost: 5,
            usesAmmo: true,
            ammoType: 'plasma_cartridge',
            magazineSize: 16,
            currentAmmo: 16,
            reloadAPCost: 4,
            durability: 60,
            maxDurability: 80,
            degradationRate: 1.5,
            armorPenetration: 20,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 6,
            PParamRequired: 7,
            EParamRequired: 6,
            IParamRequired: 8,
            AParamRequired: 5,
        },
        attackModel: 'single',
        modificationSlots: ['barrel', 'scope'],
        installedMods: [],
        maxMods: 2,
        specialAbilities: ['plasma_burn'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { electronic_parts: 5, plasma_cartridge: 3, rare_metals: 2 },
        manufacturer: 'Enclave R&D',
        yearMade: 2242,
        loreText: 'Секретная разработка Анклава. Способно прожечь даже силовую броню.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: "weap_knife_basic",
        name: "Простой нож",
        description: "Прекрасно подходит для создания салатов.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 1.0,
        value: 100,
        weaponRange: "melee",
        weaponType: "melee",
        weaponClass: "blade",
        stats: {
            damage: { min: 6, max: 12, type: "normal" },
            accuracy: 85,
            criticalChance: 8,
            criticalMultiplier: 1.8,
            range: 1,
            actionPointCost: 3,
            usesAmmo: false,
            durability: 60,
            maxDurability: 60,
            degradationRate: 0.05,
            armorPenetration: 5,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 1,
            PParamRequired: 2,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 2
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        loreText: "Не самый лучший выбор, но он вам подойдет.",
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    }, {
        id: "weap_crowbar",
        name: "Фомка",
        description: "Универсальный инструмент и неплохое оружие.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 2.8,
        value: 120,
        weaponRange: "melee",
        weaponType: "melee",
        weaponClass: "club",
        stats: {
            damage: { min: 10, max: 18, type: "normal" },
            accuracy: 65,
            criticalChance: 5,
            criticalMultiplier: 1.5,
            range: 1,
            actionPointCost: 4,
            usesAmmo: false,
            durability: 80,
            maxDurability: 80,
            degradationRate: 0.03,
            armorPenetration: 8,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 2,
            PParamRequired: 1,
            EParamRequired: 1,
            IParamRequired: 0,
            AParamRequired: 1
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: 'combat-knife',
        name: 'Боевой нож',
        description: 'Острый тактический нож из высокоуглеродистой стали. Смертелен в умелых руках.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 0.8,
        value: 80,
        weaponRange: 'melee',
        weaponType: 'melee',
        weaponClass: 'blade',
        stats: {
            damage: { min: 10, max: 20, type: 'normal' },
            accuracy: 85,
            criticalChance: 20,
            criticalMultiplier: 4.0,
            range: 1,
            actionPointCost: 2,
            usesAmmo: false,
            durability: 95,
            maxDurability: 120,
            degradationRate: 0.2,
            armorPenetration: 2,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 3,
            PParamRequired: 4,
            EParamRequired: 4,
            IParamRequired: 3,
            AParamRequired: 6,
        },
        attackModel: 'single',
        modificationSlots: ['grip'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ['silent_kill'],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'metal-parts': 3, leather: 1 },
            skill: { repair: 25 },
            tools: ['workbench'],
        },
        canBeRepaired: true,
        manufacturer: 'Ka-Bar',
        yearMade: 2076,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: "weap_brass_knuckles",
        name: "Кастет",
        description: "Простой, но эффективный способ познакомиться ближе.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 0.8,
        value: 75,
        weaponRange: "melee",
        weaponType: "melee",
        weaponClass: "unarmed",
        stats: {
            damage: { min: 5, max: 10, type: "normal" },
            accuracy: 80,
            criticalChance: 5,
            criticalMultiplier: 1.5,
            range: 1,
            actionPointCost: 3,
            usesAmmo: false,
            durability: 60,
            maxDurability: 60,
            degradationRate: 0.05,
            armorPenetration: 3,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 1,
            PParamRequired: 1,
            EParamRequired: 0,
            IParamRequired: 0,
            AParamRequired: 2
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: "weap_baseball_bat",
        name: "Бейсбольная бита",
        description: "Старый добрый аргумент в споре.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 3.2,
        value: 150,
        weaponRange: "melee",
        weaponType: "melee",
        weaponClass: "club",
        stats: {
            damage: { min: 20, max: 30, type: "normal" },
            accuracy: 70,
            criticalChance: 6,
            criticalMultiplier: 1.6,
            range: 1,
            actionPointCost: 4,
            usesAmmo: false,
            durability: 90,
            maxDurability: 90,
            degradationRate: 0.04,
            armorPenetration: 5,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 2,
            PParamRequired: 1,
            EParamRequired: 1,
            IParamRequired: 0,
            AParamRequired: 1
        },
        attackModel: "single",
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        canBeRepaired: true,
        loreText: "",
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: "weap_pneumatic_knuckles",
        name: "Пневматический кастет",
        description: "Механический хук с компрессией. Когда одного удара мало — дай второй… с давлением.",
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 1.5,
        value: 500,
        weaponRange: "melee",
        weaponType: "melee",
        weaponClass: "unarmed",
        stats: {
            damage: { min: 12, max: 22, type: "normal" },
            accuracy: 75,
            criticalChance: 12,
            criticalMultiplier: 2.0,
            range: 1,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: "compressed_air",
            magazineSize: 5,
            currentAmmo: 5,
            reloadAPCost: 3,
            durability: 85,
            maxDurability: 85,
            degradationRate: 0.04,
            armorPenetration: 6,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 3,
            PParamRequired: 2,
            EParamRequired: 0,
            IParamRequired: 1,
            AParamRequired: 2
        },
        attackModel: "single",
        modificationSlots: ["grip"],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ["stagger_on_hit"],
        canBeCrafted: false,
        canBeRepaired: true,
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date()
    },
    {
        id: 'super-sledge',
        name: 'Супер-кувалда',
        description: 'Тяжелая кувалда с ракетным двигателем. Может раздробить кости мутанта.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 12.0,
        value: 1800,
        weaponRange: 'melee',
        weaponType: 'melee',
        weaponClass: 'club',
        stats: {
            damage: { min: 40, max: 80, type: 'normal' },
            accuracy: 60,
            criticalChance: 15,
            criticalMultiplier: 3.0,
            range: 1,
            actionPointCost: 6,
            usesAmmo: false,
            durability: 80,
            maxDurability: 100,
            degradationRate: 0.8,
            armorPenetration: 15,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 8,
            PParamRequired: 4,
            EParamRequired: 7,
            IParamRequired: 4,
            AParamRequired: 3,
        },
        attackModel: 'single',
        modificationSlots: ['grip'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ['knockdown'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 8, fuel: 2 },
        manufacturer: 'Vault-Tec',
        yearMade: 2077,
        loreText: 'Экспериментальное оружие, разработанное для работ в Убежищах.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'minigun',
        name: 'Миниган',
        description: 'Многоствольный пулемет с электроприводом. Уничтожает все на своем пути.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.LEGENDARY,
        weight: 18.0,
        value: 4000,
        weaponRange: 'ranged',
        weaponType: 'heavy ballistic',
        weaponClass: 'lmg',
        stats: {
            damage: { min: 8, max: 12, type: 'normal' },
            accuracy: 45,
            criticalChance: 3,
            criticalMultiplier: 1.5,
            range: 12,
            actionPointCost: 8,
            usesAmmo: true,
            ammoType: '5mm',
            magazineSize: 500,
            currentAmmo: 500,
            reloadAPCost: 6,
            durability: 50,
            maxDurability: 70,
            degradationRate: 2.0,
            armorPenetration: 5,
            burstFire: true,
            burstSize: 10,
            fullAuto: true,
            SParamRequired: 8,
            PParamRequired: 5,
            EParamRequired: 8,
            IParamRequired: 4,
            AParamRequired: 3,
        },
        attackModel: 'full-auto',
        modificationSlots: ['barrel'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ['suppression'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 12, electronic_parts: 4 },
        manufacturer: 'General Atomics',
        yearMade: 2077,
        loreText: 'Тяжелое оружие поддержки. Способно остановить целую армию.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'fat-man',
        name: 'Толстяк',
        description: 'Портативная катапульта для мини-ядерных зарядов. Оружие массового поражения.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.LEGENDARY,
        weight: 30.0,
        value: 15000,
        weaponRange: 'ranged',
        weaponType: 'explosive',
        weaponClass: 'launcher',
        stats: {
            damage: { min: 200, max: 500, type: 'explosive' },
            accuracy: 60,
            criticalChance: 1,
            criticalMultiplier: 2.0,
            range: 25,
            actionPointCost: 10,
            usesAmmo: true,
            ammoType: 'mini_nuke',
            magazineSize: 1,
            currentAmmo: 1,
            reloadAPCost: 8,
            durability: 80,
            maxDurability: 100,
            degradationRate: 1.0,
            armorPenetration: 50,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 9,
            PParamRequired: 6,
            EParamRequired: 7,
            IParamRequired: 7,
            AParamRequired: 3,
        },
        attackModel: 'single',
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: ['nuclear_explosion', 'radiation_damage'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 20, electronic_parts: 10, rare_metals: 5 },
        manufacturer: 'General Atomics',
        yearMade: 2077,
        loreText: 'Лучшее для Худших',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
    {
        id: 'ripper',
        name: 'Потрошитель',
        description: 'Цепная пила, переделанная в оружие. Может распополамить трактор.',
        type: enums_1.ItemType.WEAPON,
        rarity: enums_1.ItemRarity.RARE,
        weight: 6.0,
        value: 800,
        weaponRange: 'melee',
        weaponType: 'melee',
        weaponClass: 'club',
        stats: {
            damage: { min: 15, max: 30, type: 'normal' },
            accuracy: 70,
            criticalChance: 25,
            criticalMultiplier: 2.0,
            range: 1,
            actionPointCost: 4,
            usesAmmo: true,
            ammoType: 'fuel',
            magazineSize: 20,
            currentAmmo: 20,
            reloadAPCost: 3,
            durability: 60,
            maxDurability: 80,
            degradationRate: 1.5,
            armorPenetration: 8,
            burstFire: false,
            fullAuto: false,
            SParamRequired: 6,
            PParamRequired: 5,
            EParamRequired: 6,
            IParamRequired: 4,
            AParamRequired: 5,
        },
        attackModel: 'single',
        modificationSlots: ['grip'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ['fear_effect', 'bleeding'],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'metal-parts': 15, electronic_parts: 5, fuel: 5 },
            skill: { repair: 75 },
            tools: ['workbench'],
        },
        canBeRepaired: true,
        repairCost: { 'metal-parts': 4, fuel: 2 },
        manufacturer: 'Converted Tool',
        loreText: 'Инструмент, превращенный в оружие отчаянными выжившими.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        createdAt: new Date(),
    },
];
//# sourceMappingURL=weapons.js.map