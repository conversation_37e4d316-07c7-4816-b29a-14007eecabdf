import { ContainerType, LocationCellLandType, LocationType, TerrainType } from '../enums';
import { Position } from '../models/Player';
import { NPC } from './NPC';
export interface LocationExit {
    id: string;
    name: string;
    description: string;
    targetLocationId: string;
    position: Position;
    isLocked: boolean;
    keyRequired?: string;
    skillRequired?: {
        skill: string;
        level: number;
    };
    isHidden: boolean;
    discoveryRequirement?: string;
}
export interface LocationContainer {
    id: string;
    name: string;
    textureType: ContainerType;
    description: string;
    position: Position;
    isLocked: boolean;
    keyRequired?: string;
    skillRequired?: {
        skill: string;
        level: number;
    };
    contents: string[];
    isLooted: boolean;
    respawnTime?: number;
    lastLootedAt?: Date;
    containerRef?: string;
}
export interface LocationHazard {
    id: string;
    type: 'radiation' | 'toxic' | 'fire' | 'electric' | 'trap';
    position: Position;
    radius: number;
    damage: number;
    isActive: boolean;
    canBeDisabled: boolean;
    disableRequirement?: {
        skill: string;
        level: number;
        item?: string;
    };
}
export interface LocationEvent {
    id: string;
    name: string;
    description: string;
    triggerConditions: {
        playerLevel?: number;
        questCompleted?: string;
        itemInInventory?: string;
        timeOfDay?: 'day' | 'night';
        visitCount?: number;
    };
    isTriggered: boolean;
    isRepeatable: boolean;
    consequences: {
        spawnNPCs?: string[];
        addItems?: string[];
        startQuest?: string;
        changeReputation?: Record<string, number>;
    };
}
export interface Location {
    id: string;
    name: string;
    description: string;
    type: LocationType;
    locationSize: LocationSize;
    locationMap: Record<string, LocationMapCell>;
    terrain: TerrainType;
    constructions?: Record<string, string>;
    isDiscovered: boolean;
    isVisible: boolean;
    isExplored: boolean;
    createdAt: Date;
    lastUpdatedAt: Date;
}
export interface LocationMapCell {
    pos: Position;
    terrain: LocationCellLandType;
    blocked: boolean;
    npc?: NPC;
    container?: LocationContainer;
    hazard?: LocationHazard;
    fogOfWar: boolean;
    goBackZone: boolean;
    spawnZone: boolean;
}
export interface LocationSize {
    width: number;
    height: number;
}
