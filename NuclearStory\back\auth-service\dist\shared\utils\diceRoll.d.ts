export interface DiceRollResult {
    result: number;
    rolls: number[];
    modifier: number;
    total: number;
    isCriticalSuccess: boolean;
    isCriticalFailure: boolean;
    isSuccess: boolean;
    difficulty?: number;
}
export declare function rollDie(sides: number): number;
export declare function rollDice(count: number, sides: number): number[];
export declare function rollWithModifier(count: number, sides: number, modifier?: number, difficulty?: number): DiceRollResult;
export declare function d20(modifier?: number, difficulty?: number): DiceRollResult;
export declare function d100(modifier?: number, difficulty?: number): DiceRollResult;
export declare function rollDamage(count: number, sides: number, modifier?: number): number;
export declare function skillCheck(skillLevel: number, statValue: number, difficulty: number, modifier?: number): DiceRollResult;
export declare function attributeCheck(statValue: number, difficulty: number, modifier?: number): DiceRollResult;
export declare function luckRoll(luckValue: number, baseChance?: number): boolean;
export declare function encounterRoll(encounterChance: number): boolean;
export declare function criticalHitRoll(criticalChance: number, luckModifier?: number): boolean;
export declare function durabilityRoll(currentDurability: number, maxDurability: number): number;
export declare function lootRoll(lootChance: number, qualityModifier?: number): {
    hasLoot: boolean;
    quality: 'poor' | 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
};
