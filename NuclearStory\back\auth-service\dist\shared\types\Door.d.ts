import { Position } from '../models/Player';
export interface DoorLock {
    type: 'key' | 'keycard' | 'code' | 'biometric' | 'combination' | 'electronic' | 'magical';
    difficulty: number;
    requirements: {
        keyId?: string;
        keycardLevel?: number;
        code?: string;
        biometricId?: string;
        skillLevel?: number;
        toolRequired?: string;
    };
    isLocked: boolean;
    isBroken: boolean;
    isJammed: boolean;
    failedAttempts: number;
    maxAttempts: number;
    lockoutTime?: number;
    hasAlarm: boolean;
    alarmTriggered: boolean;
    isMonitored: boolean;
}
export interface DoorMaterial {
    type: 'wood' | 'metal' | 'reinforced_metal' | 'glass' | 'plastic' | 'composite' | 'energy_barrier';
    durability: number;
    maxDurability: number;
    resistances: {
        physical: number;
        fire: number;
        acid: number;
        explosion: number;
        energy: number;
    };
    isTransparent: boolean;
    isSoundproof: boolean;
    isFireproof: boolean;
    isRadiationShielded: boolean;
}
export interface DoorAnimation {
    openType: 'swing' | 'slide' | 'fold' | 'roll' | 'iris' | 'phase' | 'teleport';
    openDirection: 'inward' | 'outward' | 'left' | 'right' | 'up' | 'down';
    openSpeed: number;
    closeSpeed: number;
    autoClose: boolean;
    autoCloseDelay: number;
    openSound?: string;
    closeSound?: string;
    lockedSound?: string;
    breakSound?: string;
}
export interface Door {
    id: string;
    name: string;
    description: string;
    position: Position;
    rotation: number;
    width: number;
    height: number;
    thickness: number;
    isOpen: boolean;
    isDestroyed: boolean;
    isBlocked: boolean;
    blockingObjectId?: string;
    material: DoorMaterial;
    lock?: DoorLock;
    animation: DoorAnimation;
    connections: {
        leadsToLocation?: {
            locationId: string;
            targetPosition: Position;
            targetLevel?: number;
        };
        leadsToLevel?: {
            targetLevel: number;
            targetPosition: Position;
        };
        leadsToCell?: {
            targetPosition: Position;
            sameLevel: boolean;
        };
    };
    access: {
        allowedFactions: string[];
        allowedNPCs: string[];
        allowedPlayers: string[];
        requiresPermission: boolean;
        permissionLevel: number;
        accessSchedule?: Array<{
            startHour: number;
            endHour: number;
            daysOfWeek: number[];
        }>;
    };
    interaction: {
        isInteractable: boolean;
        interactionRange: number;
        interactionTime: number;
        requirements?: {
            strength?: number;
            skill?: string;
            tool?: string;
            energy?: number;
        };
    };
    effects: {
        onOpen?: {
            triggerAlarm?: boolean;
            alertNPCs?: string[];
            spawnEnemies?: string[];
            giveItems?: string[];
            startQuest?: string;
            changeReputation?: Record<string, number>;
        };
        onClose?: {
            lockAutomatically?: boolean;
            sealPermanently?: boolean;
            triggerTrap?: string;
        };
        onDestroy?: {
            createDebris?: boolean;
            blockPassage?: boolean;
            triggerExplosion?: boolean;
            alertArea?: number;
        };
    };
    visual: {
        sprite?: string;
        model?: string;
        texture?: string;
        glowWhenLocked?: boolean;
        sparkWhenDamaged?: boolean;
        smokeWhenDestroyed?: boolean;
        hasStatusLight?: boolean;
        statusLightColor?: string;
    };
    history: {
        timesOpened: number;
        timesLocked: number;
        timesBroken: number;
        lastOpenedAt?: Date;
        lastLockedAt?: Date;
        lastRepairedAt?: Date;
        lastInteractedBy?: {
            type: 'player' | 'npc';
            id: string;
            action: 'open' | 'close' | 'lock' | 'unlock' | 'break' | 'repair';
        };
    };
    createdAt: Date;
    lastUpdatedAt: Date;
    version: string;
}
