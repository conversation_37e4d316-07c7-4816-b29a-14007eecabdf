import { InventoryItem } from './InventoryItem';
import { EffectType } from '../enums';
import { Position } from '../types/Common';
export { Position };
export interface Effect {
    id: string;
    type: EffectType;
    duration: number;
    value: number;
    description: string;
}
export interface Special {
    S: number;
    P: number;
    E: number;
    C: number;
    I: number;
    A: number;
    L: number;
}
export interface Player {
    id: string;
    name: string;
    level: number;
    experience: number;
    experienceToNext: number;
    special: Special;
    currentHP: number;
    maxHP: number;
    currentAP: number;
    maxAP: number;
    radiationLevel: number;
    hunger: number;
    thirst: number;
    fatigue: number;
    perks: string[];
    skills: Record<string, number>;
    inventory: InventoryItem[];
    equippedWeapon?: string;
    equippedArmor?: string;
    position: Position;
    currentLocationId?: string;
    effects: Effect[];
    completedQuests: string[];
    activeQuests: string[];
    discoveredLocations: string[];
    factionReputation: Record<string, number>;
    createdAt: Date;
    lastSaveAt: Date;
}
