import { BattlePhase, TerrainType } from '../enums';
import { Position } from './Player';
export interface BattleCell {
    position: Position;
    terrain: TerrainType;
    isBlocked: boolean;
    hasObstacle: boolean;
    obstacleType?: string;
    movementCost: number;
    coverBonus: number;
}
export interface BattleUnit {
    id: string;
    name: string;
    type: 'player' | 'npc' | 'enemy';
    position: Position;
    movementRange: number;
    hasActed: boolean;
    hasMoved: boolean;
    currentHP: number;
    maxHP: number;
    currentAP: number;
    maxAP: number;
    initiative: number;
    attackRange: number;
    damage: number;
    accuracy: number;
    criticalChance: number;
    armor: number;
    isAlive: boolean;
    isStunned: boolean;
    statusEffects: string[];
    equippedWeapon?: string;
    equippedArmor?: string;
    aiType?: 'aggressive' | 'defensive' | 'support' | 'flee';
    targetId?: string;
}
export interface BattleAction {
    id: string;
    unitId: string;
    type: 'move' | 'attack' | 'use_item' | 'wait' | 'reload';
    target?: Position | string;
    itemId?: string;
    apCost: number;
    timestamp: Date;
}
export interface BattleArena {
    id: string;
    name: string;
    description: string;
    gridSize: {
        width: number;
        height: number;
    };
    cells: BattleCell[][];
    phase: BattlePhase;
    currentTurn: number;
    maxTurns: number;
    timeLimit?: number;
    units: BattleUnit[];
    playerUnits: string[];
    enemyUnits: string[];
    initiativeOrder: string[];
    currentUnitIndex: number;
    currentUnitId: string;
    actions: BattleAction[];
    battleLog: string[];
    victoryConditions: {
        eliminateAllEnemies: boolean;
        surviveTurns?: number;
        protectTarget?: string;
        reachPosition?: Position;
    };
    isCompleted: boolean;
    winner?: 'player' | 'enemy' | 'draw';
    rewards?: {
        experience: number;
        items: string[];
        currency: number;
    };
    environmentEffects: {
        radiation: number;
        visibility: number;
        weather: string;
        timeOfDay: 'day' | 'night' | 'dawn' | 'dusk';
    };
    startedAt: Date;
    endedAt?: Date;
    lastActionAt: Date;
}
