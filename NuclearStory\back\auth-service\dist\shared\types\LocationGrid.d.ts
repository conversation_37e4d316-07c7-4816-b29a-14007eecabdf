import { Position } from './Common';
import { TerrainType } from '../enums';
export interface LocationObject {
    id: string;
    type: 'container' | 'door' | 'npc' | 'furniture' | 'decoration' | 'hazard' | 'interactive';
    name: string;
    description?: string;
    position: Position;
    sprite?: string;
    model?: string;
    rotation: number;
    scale: number;
    isBlocking: boolean;
    isInteractable: boolean;
    hasCollision: boolean;
    isActive: boolean;
    isVisible: boolean;
    isDestroyed: boolean;
    objectData?: Record<string, any>;
    createdAt: Date;
    lastInteractedAt?: Date;
}
export interface LocationCell {
    position: Position;
    terrain: TerrainType;
    floorType: string;
    elevation: number;
    level: number;
    isPassable: boolean;
    movementCost: number;
    requiresClimbing: boolean;
    requiresSwimming: boolean;
    lightLevel: number;
    lightSources: string[];
    objects: string[];
    wallType?: string;
    ceilingType?: string;
    hasRoof: boolean;
    temperature: number;
    humidity: number;
    radiationLevel: number;
    airQuality: number;
    ambientSounds: string[];
    soundLevel: number;
    scents: Array<{
        type: string;
        intensity: number;
        age: number;
    }>;
    tracks: Array<{
        creatureType: string;
        direction: number;
        age: number;
        size: number;
    }>;
    isDamaged: boolean;
    damageLevel: number;
    isOnFire: boolean;
    isFlooded: boolean;
    isFrozen: boolean;
    isVisible: boolean;
    isExplored: boolean;
    fogOfWar: boolean;
    lastVisitedAt?: Date;
    isSpawnPoint: boolean;
    isExitPoint: boolean;
    isLandmark: boolean;
    lastUpdatedAt: Date;
}
export interface LocationLevel {
    level: number;
    name: string;
    description?: string;
    gridSize: {
        width: number;
        height: number;
    };
    cells: LocationCell[][];
    objects: Record<string, LocationObject>;
    connections: Array<{
        type: 'stairs' | 'ladder' | 'elevator' | 'ramp' | 'hole';
        position: Position;
        targetLevel: number;
        targetPosition: Position;
        isBlocked: boolean;
        requiresKey?: string;
    }>;
    globalLighting: {
        ambientLight: number;
        hasElectricity: boolean;
        powerSources: string[];
    };
    climate: {
        temperature: number;
        humidity: number;
        airCirculation: number;
        hasVentilation: boolean;
    };
}
export interface LocationGrid {
    locationId: string;
    totalSize: {
        width: number;
        height: number;
        levels: number;
    };
    levels: Record<number, LocationLevel>;
    globalObjects: Record<string, LocationObject>;
    systems: {
        electrical: {
            hasPower: boolean;
            powerLevel: number;
            generators: string[];
            powerGrid: Array<{
                from: Position;
                to: Position;
                isActive: boolean;
            }>;
        };
        plumbing: {
            hasWater: boolean;
            waterPressure: number;
            waterSources: string[];
            pipes: Array<{
                from: Position;
                to: Position;
                isWorking: boolean;
            }>;
        };
        ventilation: {
            airQuality: number;
            ventilationRate: number;
            vents: string[];
        };
        security: {
            alarmSystem: boolean;
            cameras: string[];
            locks: string[];
            securityLevel: number;
        };
    };
    zones: Array<{
        id: string;
        name: string;
        type: 'residential' | 'commercial' | 'industrial' | 'storage' | 'security' | 'medical' | 'recreational';
        bounds: {
            minX: number;
            minY: number;
            maxX: number;
            maxY: number;
            level: number;
        };
        properties: Record<string, any>;
    }>;
    pathfinding: {
        walkableAreas: Array<{
            level: number;
            bounds: {
                minX: number;
                minY: number;
                maxX: number;
                maxY: number;
            };
        }>;
        blockedAreas: Array<{
            level: number;
            bounds: {
                minX: number;
                minY: number;
                maxX: number;
                maxY: number;
            };
            reason: string;
        }>;
        shortcuts: Array<{
            from: Position;
            to: Position;
            cost: number;
            requirements?: string[];
        }>;
    };
    generatedAt: Date;
    lastUpdatedAt: Date;
    version: string;
}
