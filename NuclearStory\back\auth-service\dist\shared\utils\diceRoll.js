"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rollDie = rollDie;
exports.rollDice = rollDice;
exports.rollWithModifier = rollWithModifier;
exports.d20 = d20;
exports.d100 = d100;
exports.rollDamage = rollDamage;
exports.skillCheck = skillCheck;
exports.attributeCheck = attributeCheck;
exports.luckRoll = luckRoll;
exports.encounterRoll = encounterRoll;
exports.criticalHitRoll = criticalHitRoll;
exports.durabilityRoll = durabilityRoll;
exports.lootRoll = lootRoll;
function rollDie(sides) {
    return Math.floor(Math.random() * sides) + 1;
}
function rollDice(count, sides) {
    const rolls = [];
    for (let i = 0; i < count; i++) {
        rolls.push(rollDie(sides));
    }
    return rolls;
}
function rollWithModifier(count, sides, modifier = 0, difficulty) {
    const rolls = rollDice(count, sides);
    const result = rolls.reduce((sum, roll) => sum + roll, 0);
    const total = result + modifier;
    const isCriticalSuccess = sides === 20 && rolls.some(roll => roll === 20);
    const isCriticalFailure = sides === 20 && rolls.some(roll => roll === 1);
    const isSuccess = difficulty !== undefined ? total >= difficulty : false;
    return {
        result,
        rolls,
        modifier,
        total,
        isCriticalSuccess,
        isCriticalFailure,
        isSuccess,
        difficulty
    };
}
function d20(modifier = 0, difficulty) {
    return rollWithModifier(1, 20, modifier, difficulty);
}
function d100(modifier = 0, difficulty) {
    return rollWithModifier(1, 100, modifier, difficulty);
}
function rollDamage(count, sides, modifier = 0) {
    const result = rollWithModifier(count, sides, modifier);
    return Math.max(1, result.total);
}
function skillCheck(skillLevel, statValue, difficulty, modifier = 0) {
    const totalModifier = skillLevel + statValue + modifier;
    return d20(totalModifier, difficulty);
}
function attributeCheck(statValue, difficulty, modifier = 0) {
    return d20(statValue + modifier, difficulty);
}
function luckRoll(luckValue, baseChance = 50) {
    const roll = d100();
    const adjustedChance = baseChance + (luckValue - 5) * 5;
    return roll.total <= adjustedChance;
}
function encounterRoll(encounterChance) {
    const roll = d100();
    return roll.total <= encounterChance;
}
function criticalHitRoll(criticalChance, luckModifier = 0) {
    const adjustedChance = criticalChance + luckModifier;
    const roll = d100();
    return roll.total <= adjustedChance;
}
function durabilityRoll(currentDurability, maxDurability) {
    const conditionPercent = (currentDurability / maxDurability) * 100;
    const roll = d100();
    if (roll.total > conditionPercent) {
        return Math.max(0, currentDurability - rollDie(3));
    }
    return currentDurability;
}
function lootRoll(lootChance, qualityModifier = 0) {
    const roll = d100();
    const hasLoot = roll.total <= lootChance;
    if (!hasLoot) {
        return { hasLoot: false, quality: 'poor' };
    }
    const qualityRoll = d100(qualityModifier);
    let quality;
    if (qualityRoll.total >= 95)
        quality = 'legendary';
    else if (qualityRoll.total >= 85)
        quality = 'epic';
    else if (qualityRoll.total >= 70)
        quality = 'rare';
    else if (qualityRoll.total >= 50)
        quality = 'uncommon';
    else if (qualityRoll.total >= 25)
        quality = 'common';
    else
        quality = 'poor';
    return { hasLoot, quality };
}
//# sourceMappingURL=diceRoll.js.map