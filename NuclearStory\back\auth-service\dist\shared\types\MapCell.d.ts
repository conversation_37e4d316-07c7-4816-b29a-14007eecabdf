import { TerrainType } from '../enums';
import { Position } from './Common';
export interface MapCellResource {
    type: string;
    amount: number;
    quality: number;
    extractionDifficulty: number;
    renewsOverTime: boolean;
    renewalRate?: number;
}
export interface MapCellStructure {
    id: string;
    type: string;
    name: string;
    isRuined: boolean;
    canEnter: boolean;
    entrancePosition?: Position;
}
export interface MapCellEvent {
    id: string;
    type: 'encounter' | 'discovery' | 'hazard' | 'treasure';
    probability: number;
    isTriggered: boolean;
    triggerConditions?: {
        timeOfDay?: 'day' | 'night';
        weather?: string;
        playerLevel?: number;
        questActive?: string;
    };
}
export interface MapCell {
    coord: Position;
    terrain: TerrainType;
    elevation: number;
    temperature: number;
    radiationLevel: number;
    isVisible: boolean;
    isExplored: boolean;
    fogOfWar: boolean;
    lastVisitedAt?: Date;
    hasLocation: boolean;
    locationId?: string;
    structures: MapCellStructure[];
    isPassable: boolean;
    movementCost: number;
    requiresSpecialEquipment?: string;
    resources: MapCellResource[];
    hasLoot: boolean;
    lootTableId?: string;
    isLooted: boolean;
    lootRespawnTime?: number;
    npcs: string[];
    maxNPCs: number;
    enemySpawnChance: number;
    enemyTypes: string[];
    events: MapCellEvent[];
    weatherEffects: {
        windSpeed: number;
        precipitation: number;
        visibility: number;
    };
    roads: string[];
    tradeRoutes: string[];
    controlledBy?: string;
    isContested: boolean;
    influenceLevel: Record<string, number>;
    isLandmark: boolean;
    landmarkName?: string;
    isStrategic: boolean;
    defensiveBonus: number;
    isFastTravelPoint: boolean;
    fastTravelName?: string;
    discoveredAt?: Date;
    lastUpdatedAt: Date;
}
