{"version": 3, "file": "SaveData.js", "sourceRoot": "", "sources": ["../../../src/shared/models/SaveData.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA0G;AAC1G,yDAAyC;AACzC,oCAAsC;AAEtC,mCAAgC;AAGhC,MAAa,YAAY;IAqBvB,YAAY,UAAiC,EAAE;QAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAxBD,oCAwBC;AAtBC;IADC,IAAA,0BAAQ,GAAE;;wCACA;AAGX;IADC,IAAA,0BAAQ,GAAE;;0CACE;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;iDACO;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACU;AAKrB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qDACE;AAO7B,MAAa,YAAY;IA2BvB,YAAY,UAAiC,EAAE;QAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AA9BD,oCA8BC;AA5BC;IADC,IAAA,0BAAQ,GAAE;;2CACG;AAGd;IADC,IAAA,0BAAQ,GAAE;;gDACQ;AAGnB;IADC,IAAA,0BAAQ,GAAE;;sDACc;AAIzB;IAFC,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qDACC;AAI1B;IAFC,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;yDACK;AAI9B;IAFC,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;0DACM;AAK/B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;gDACH;AAOxB,MAAa,QAAQ;IA+DnB,YAAY,UAA6B,EAAE;QACzC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAlED,4BAkEC;AAhEC;IADC,IAAA,0BAAQ,GAAE;;oCACA;AAGX;IADC,IAAA,0BAAQ,GAAE;;wCACI;AAGf;IADC,IAAA,0BAAQ,GAAE;;0CACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACU;AAGrB;IADC,IAAA,0BAAQ,GAAE;;yCACK;AAGhB;IADC,IAAA,wBAAM,EAAC,kBAAU,CAAC;;4CACI;AAIvB;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;2CACE;AAIrB;IAFC,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC;8BACf,YAAY;0CAAC;AAGvB;IADC,IAAA,0BAAQ,GAAE;;mDACe;AAK1B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;iDACW;AAK9B;IAHC,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAK,CAAC;;8CACI;AAKtB;IAHC,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;2CACc;AAGjC;IADC,IAAA,0BAAQ,GAAE;;iDACa;AAGxB;IADC,IAAA,wBAAM,GAAE;8BACE,IAAI;2CAAC;AAGhB;IADC,IAAA,wBAAM,GAAE;8BACE,IAAI;2CAAC;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;8BACM,IAAI;8CAAC;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4CACS;AAOtB,MAAa,iBAAiB;CAgB7B;AAhBD,8CAgBC;AAdC;IADC,IAAA,0BAAQ,GAAE;;mDACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACU;AAKrB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;oDACG;AAItB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACgB;AAG7B,MAAa,iBAAiB;CAmD7B;AAnDD,8CAmDC;AAhDC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,kBAAU,CAAC;;qDACK;AAKxB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;oDACG;AAKtB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC;8BACd,YAAY;mDAAC;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACgB;AAK3B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;0DACW;AAM9B;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAK,CAAC;;uDACK;AAMvB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;oDACe;AAIlC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACc;AAIzB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACS"}