"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattlePhase = exports.EffectType = exports.ContainerType = exports.FactionAlignment = exports.NPCType = exports.LocationCellLandType = exports.TerrainType = exports.LocationType = exports.ItemRarity = exports.ItemType = exports.EventType = exports.QuestType = exports.QuestStatus = exports.Language = exports.GameStatus = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["PLAYER"] = "player";
    UserRole["ADMIN"] = "admin";
    UserRole["MODERATOR"] = "moderator";
})(UserRole || (exports.UserRole = UserRole = {}));
var GameStatus;
(function (GameStatus) {
    GameStatus["NOT_STARTED"] = "not_started";
    GameStatus["IN_PROGRESS"] = "in_progress";
    GameStatus["PAUSED"] = "paused";
    GameStatus["COMPLETED"] = "completed";
    GameStatus["GAME_OVER"] = "game_over";
})(GameStatus || (exports.GameStatus = GameStatus = {}));
var Language;
(function (Language) {
    Language["EN"] = "en";
    Language["PL"] = "pl";
    Language["UK"] = "uk";
    Language["RU"] = "ru";
    Language["DE"] = "de";
    Language["FR"] = "fr";
    Language["ES"] = "es";
    Language["CN"] = "cn";
    Language["JP"] = "jp";
})(Language || (exports.Language = Language = {}));
var QuestStatus;
(function (QuestStatus) {
    QuestStatus["AVAILABLE"] = "available";
    QuestStatus["ACTIVE"] = "active";
    QuestStatus["COMPLETED"] = "completed";
    QuestStatus["FAILED"] = "failed";
    QuestStatus["LOCKED"] = "locked";
})(QuestStatus || (exports.QuestStatus = QuestStatus = {}));
var QuestType;
(function (QuestType) {
    QuestType["MAIN"] = "main";
    QuestType["SIDE"] = "side";
    QuestType["RANDOM"] = "random";
})(QuestType || (exports.QuestType = QuestType = {}));
var EventType;
(function (EventType) {
    EventType["STORY"] = "story";
    EventType["COMBAT"] = "combat";
    EventType["CHOICE"] = "choice";
    EventType["DISCOVERY"] = "discovery";
})(EventType || (exports.EventType = EventType = {}));
var ItemType;
(function (ItemType) {
    ItemType["CONSUMABLE"] = "consumable";
    ItemType["WEAPON"] = "weapon";
    ItemType["ARMOR"] = "armor";
    ItemType["TOOL"] = "tool";
    ItemType["RESOURCE"] = "resource";
    ItemType["QUEST_ITEM"] = "quest_item";
})(ItemType || (exports.ItemType = ItemType = {}));
var ItemRarity;
(function (ItemRarity) {
    ItemRarity["COMMON"] = "common";
    ItemRarity["UNCOMMON"] = "uncommon";
    ItemRarity["RARE"] = "rare";
    ItemRarity["EPIC"] = "epic";
    ItemRarity["LEGENDARY"] = "legendary";
})(ItemRarity || (exports.ItemRarity = ItemRarity = {}));
var LocationType;
(function (LocationType) {
    LocationType["OUTDOOR"] = "outdoor";
    LocationType["INDOOR"] = "indoor";
    LocationType["UNDERGROUND"] = "underground";
    LocationType["SETTLEMENT"] = "settlement";
})(LocationType || (exports.LocationType = LocationType = {}));
var TerrainType;
(function (TerrainType) {
    TerrainType["GRASS"] = "grass";
    TerrainType["DEADFOREST"] = "deadforest";
    TerrainType["MOUNTAIN"] = "mountain";
    TerrainType["WATER"] = "water";
    TerrainType["DESERT"] = "desert";
    TerrainType["SWAMP"] = "swamp";
    TerrainType["ROAD"] = "road";
    TerrainType["CITY"] = "city";
    TerrainType["RUINS"] = "ruins";
    TerrainType["WASTELAND"] = "wasteland";
})(TerrainType || (exports.TerrainType = TerrainType = {}));
var LocationCellLandType;
(function (LocationCellLandType) {
    LocationCellLandType["rocky"] = "rocky";
    LocationCellLandType["sandy"] = "sandy";
    LocationCellLandType["grassy"] = "grassy";
    LocationCellLandType["muddy"] = "muddy";
    LocationCellLandType["snowy"] = "snowy";
    LocationCellLandType["swampy"] = "swampy";
    LocationCellLandType["whole"] = "whole";
    LocationCellLandType["water"] = "water";
    LocationCellLandType["shore"] = "shore";
    LocationCellLandType["none"] = "none";
})(LocationCellLandType || (exports.LocationCellLandType = LocationCellLandType = {}));
var NPCType;
(function (NPCType) {
    NPCType["TRADER"] = "trader";
    NPCType["GUARD"] = "guard";
    NPCType["CIVILIAN"] = "civilian";
    NPCType["ENEMY"] = "enemy";
    NPCType["QUEST_GIVER"] = "quest_giver";
})(NPCType || (exports.NPCType = NPCType = {}));
var FactionAlignment;
(function (FactionAlignment) {
    FactionAlignment["HOSTILE"] = "hostile";
    FactionAlignment["NEUTRAL"] = "neutral";
    FactionAlignment["FRIENDLY"] = "friendly";
    FactionAlignment["ALLIED"] = "allied";
})(FactionAlignment || (exports.FactionAlignment = FactionAlignment = {}));
var ContainerType;
(function (ContainerType) {
    ContainerType["CHEST"] = "chest";
    ContainerType["BARREL"] = "barrel";
    ContainerType["CRATE"] = "crate";
    ContainerType["SAFE"] = "safe";
    ContainerType["CORPSE"] = "corpse";
})(ContainerType || (exports.ContainerType = ContainerType = {}));
var EffectType;
(function (EffectType) {
    EffectType["HEALING"] = "healing";
    EffectType["POISON"] = "poison";
    EffectType["RADIATION"] = "radiation";
    EffectType["BUFF"] = "buff";
    EffectType["DEBUFF"] = "debuff";
})(EffectType || (exports.EffectType = EffectType = {}));
var BattlePhase;
(function (BattlePhase) {
    BattlePhase["PREPARATION"] = "preparation";
    BattlePhase["PLAYER_TURN"] = "player_turn";
    BattlePhase["ENEMY_TURN"] = "enemy_turn";
    BattlePhase["RESULT"] = "result";
    BattlePhase["ENDED"] = "ended";
})(BattlePhase || (exports.BattlePhase = BattlePhase = {}));
//# sourceMappingURL=index.js.map