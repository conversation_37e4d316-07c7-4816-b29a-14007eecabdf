import { QuestStatus, QuestType } from '../enums';
import { GameStats } from '../types';
export declare class QuestObjective {
    id: string;
    description: string;
    completed: boolean;
    targetCount?: number;
    currentCount?: number;
    constructor(partial?: Partial<QuestObjective>);
}
export declare class QuestReward {
    experience?: number;
    items?: string[];
    stats?: Partial<GameStats>;
    unlockQuests?: string[];
    unlockLocations?: string[];
    constructor(partial?: Partial<QuestReward>);
}
export declare class Quest {
    id: string;
    title: string;
    description: string;
    type: QuestType;
    status: QuestStatus;
    objectives: QuestObjective[];
    reward?: QuestReward;
    prerequisites?: string[];
    timeLimit?: number;
    location?: string;
    priority?: number;
    constructor(partial?: Partial<Quest>);
}
export declare class CreateQuestDto {
    title: string;
    description: string;
    type: QuestType;
    objectives: QuestObjective[];
    reward?: QuestReward;
    prerequisites?: string[];
    timeLimit?: number;
    location?: string;
    priority?: number;
}
export declare class UpdateQuestDto {
    status?: QuestStatus;
    objectives?: QuestObjective[];
}
