import { ItemType, ItemRarity } from '../enums';
import { GameStats } from '../types';
export interface ItemEffect {
    stats?: Partial<GameStats>;
    duration?: number;
    description?: string;
}
export interface ItemMetadata {
    icon?: string;
    model?: string;
    sound?: string;
    weight?: number;
    durability?: number;
    maxDurability?: number;
    value?: number;
    stackable?: boolean;
    tags?: string[];
}
export interface InventoryItem {
    id: string;
    name: string;
    description: string;
    type: ItemType;
    rarity: ItemRarity;
    weight: number;
    value: number;
    stackable: boolean;
    quantity?: number;
    maxStack: number;
    consumable?: boolean;
    tradeable?: boolean;
    effect?: ItemEffect;
    effects?: Array<{
        type: string;
        value: number;
        duration: number;
        description: string;
    }>;
    consumeOnUse?: boolean;
    metadata?: ItemMetadata;
    createdAt?: Date;
    usageTime?: number;
    updatedAt?: Date;
    canBeCrafted: boolean;
    canBeSold: boolean;
    canBeDropped: boolean;
    isQuestItem: boolean;
    loreText?: string;
}
export interface PlayerInventoryItem {
    id: string;
    itemId: string;
    playerId: string;
    quantity: number;
    position?: {
        slot: number;
        container?: string;
    };
    condition?: number;
    customData?: Record<string, any>;
    acquiredAt: Date;
    lastUsedAt?: Date;
}
export interface ItemTemplate {
    name: string;
    description: string;
    type: ItemType;
    rarity: ItemRarity;
    maxStack: number;
    consumable: boolean;
    tradeable: boolean;
    effect?: ItemEffect;
    metadata?: ItemMetadata;
}
