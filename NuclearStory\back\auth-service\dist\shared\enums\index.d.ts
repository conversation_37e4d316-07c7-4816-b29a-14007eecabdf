export declare enum UserRole {
    PLAYER = "player",
    ADMIN = "admin",
    MODERATOR = "moderator"
}
export declare enum GameStatus {
    NOT_STARTED = "not_started",
    IN_PROGRESS = "in_progress",
    PAUSED = "paused",
    COMPLETED = "completed",
    GAME_OVER = "game_over"
}
export declare enum Language {
    EN = "en",
    PL = "pl",
    UK = "uk",
    RU = "ru",
    DE = "de",
    FR = "fr",
    ES = "es",
    CN = "cn",
    JP = "jp"
}
export declare enum QuestStatus {
    AVAILABLE = "available",
    ACTIVE = "active",
    COMPLETED = "completed",
    FAILED = "failed",
    LOCKED = "locked"
}
export declare enum QuestType {
    MAIN = "main",
    SIDE = "side",
    RANDOM = "random"
}
export declare enum EventType {
    STORY = "story",
    COMBAT = "combat",
    CHOICE = "choice",
    DISCOVERY = "discovery"
}
export declare enum ItemType {
    CONSUMABLE = "consumable",
    WEAPON = "weapon",
    ARMOR = "armor",
    TOOL = "tool",
    RESOURCE = "resource",
    QUEST_ITEM = "quest_item"
}
export declare enum ItemRarity {
    COMMON = "common",
    UNCOMMON = "uncommon",
    RARE = "rare",
    EPIC = "epic",
    LEGENDARY = "legendary"
}
export declare enum LocationType {
    OUTDOOR = "outdoor",
    INDOOR = "indoor",
    UNDERGROUND = "underground",
    SETTLEMENT = "settlement"
}
export declare enum TerrainType {
    GRASS = "grass",
    DEADFOREST = "deadforest",
    MOUNTAIN = "mountain",
    WATER = "water",
    DESERT = "desert",
    SWAMP = "swamp",
    ROAD = "road",
    CITY = "city",
    RUINS = "ruins",
    WASTELAND = "wasteland"
}
export declare enum LocationCellLandType {
    rocky = "rocky",
    sandy = "sandy",
    grassy = "grassy",
    muddy = "muddy",
    snowy = "snowy",
    swampy = "swampy",
    whole = "whole",
    water = "water",
    shore = "shore",
    none = "none"
}
export declare enum NPCType {
    TRADER = "trader",
    GUARD = "guard",
    CIVILIAN = "civilian",
    ENEMY = "enemy",
    QUEST_GIVER = "quest_giver"
}
export declare enum FactionAlignment {
    HOSTILE = "hostile",
    NEUTRAL = "neutral",
    FRIENDLY = "friendly",
    ALLIED = "allied"
}
export declare enum ContainerType {
    CHEST = "chest",
    BARREL = "barrel",
    CRATE = "crate",
    SAFE = "safe",
    CORPSE = "corpse"
}
export declare enum EffectType {
    HEALING = "healing",
    POISON = "poison",
    RADIATION = "radiation",
    BUFF = "buff",
    DEBUFF = "debuff"
}
export declare enum BattlePhase {
    PREPARATION = "preparation",
    PLAYER_TURN = "player_turn",
    ENEMY_TURN = "enemy_turn",
    RESULT = "result",
    ENDED = "ended"
}
