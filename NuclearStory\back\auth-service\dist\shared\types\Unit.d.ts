import { NPCType, FactionAlignment } from '../enums';
import { Position, Special, Effect } from '../models/Player';
export interface UnitStats {
    special: Special;
    hitPoints: number;
    actionPoints: number;
    carryWeight: number;
    meleeDamage: number;
    damageResistance: number;
    poisonResistance: number;
    radiationResistance: number;
    sequence: number;
    healingRate: number;
    criticalChance: number;
    skills: Record<string, number>;
}
export interface UnitEquipment {
    weapon?: string;
    armor?: string;
    helmet?: string;
    gloves?: string;
    boots?: string;
    accessory?: string;
}
export interface UnitAI {
    type: 'aggressive' | 'defensive' | 'support' | 'flee' | 'patrol' | 'guard';
    aggroRange: number;
    fleeThreshold: number;
    preferredRange: 'melee' | 'ranged' | 'any';
    targetPriority: 'nearest' | 'weakest' | 'strongest' | 'player';
    useItems: boolean;
    callForHelp: boolean;
    helpRadius: number;
}
export interface UnitBehavior {
    isHostile: boolean;
    isNeutral: boolean;
    canTalk: boolean;
    canTrade: boolean;
    flees: boolean;
    callsForHelp: boolean;
    factionId?: string;
    factionRank?: string;
    alignment: FactionAlignment;
    reputation: number;
    dialogueId?: string;
    greetingText?: string;
    combatTaunts?: string[];
}
export interface Unit {
    id: string;
    name: string;
    description: string;
    type: NPCType;
    level: number;
    experience: number;
    currentHP: number;
    maxHP: number;
    currentAP: number;
    maxAP: number;
    position: Position;
    facing: 'north' | 'south' | 'east' | 'west';
    movementRange: number;
    hasActed: boolean;
    hasMoved: boolean;
    stats: UnitStats;
    equipment: UnitEquipment;
    inventory: string[];
    carryCapacity: number;
    currentWeight: number;
    isAlive: boolean;
    isConscious: boolean;
    isStunned: boolean;
    isParalyzed: boolean;
    isBlinded: boolean;
    effects: Effect[];
    isInCombat: boolean;
    lastAttacker?: string;
    combatTarget?: string;
    ai?: UnitAI;
    behavior: UnitBehavior;
    sprite?: string;
    avatar?: string;
    size: 'small' | 'medium' | 'large' | 'huge';
    lootTable?: string;
    experienceReward: number;
    currencyReward: number;
    spawnLocationId?: string;
    canRespawn: boolean;
    respawnTime?: number;
    respawnAt?: Date;
    isEssential: boolean;
    isUnique: boolean;
    isBoss: boolean;
    isMinion: boolean;
    masterId?: string;
    createdAt: Date;
    lastActionAt?: Date;
    deathAt?: Date;
}
