#!/usr/bin/env python3
"""
Simple test script for AI service
"""

import asyncio
import aiohttp
import json

async def test_ai_service():
    base_url = "http://localhost:3005"
    
    # Test health check
    print("🏥 Testing health check...")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    print("✅ Health check passed")
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Cannot connect to AI service: {e}")
            return

    # Test story generation
    print("\n📖 Testing story generation...")
    story_request = {
        "prompt": "Игрок находится в заброшенном бункере и слышит странные звуки",
        "context": {
            "location": "bunker",
            "health": 80,
            "hunger": 60,
            "thirst": 70,
            "radiation": 5,
            "inventory": ["фонарик", "консервы"]
        }
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{base_url}/generate/story",
                json=story_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                result = await response.json()
                if result.get("success"):
                    print("✅ Story generation successful")
                    print(f"📝 Generated content: {result['content'][:200]}...")
                    print(f"🤖 Provider used: {result.get('provider_used', 'unknown')}")
                else:
                    print(f"❌ Story generation failed: {result.get('error')}")
        except Exception as e:
            print(f"❌ Story generation error: {e}")

    # Test quest generation
    print("\n🎯 Testing quest generation...")
    quest_request = {
        "quest_type": "survival",
        "player_level": 5,
        "location": "wasteland",
        "context": {
            "completed_quests": ["tutorial"],
            "available_items": ["вода", "еда"]
        }
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{base_url}/generate/quest",
                json=quest_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                result = await response.json()
                if result.get("success"):
                    print("✅ Quest generation successful")
                    print(f"📝 Generated content: {result['content'][:200]}...")
                else:
                    print(f"❌ Quest generation failed: {result.get('error')}")
        except Exception as e:
            print(f"❌ Quest generation error: {e}")

if __name__ == "__main__":
    print("🧪 Testing NuclearStory AI Service")
    print("=" * 40)
    asyncio.run(test_ai_service())
    print("\n✅ Test completed!")
