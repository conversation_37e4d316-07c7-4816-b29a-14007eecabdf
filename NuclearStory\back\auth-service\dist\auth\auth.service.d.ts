import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { User } from '../users/entities/user.entity';
export interface JwtPayload {
    sub: string;
    email: string;
    username: string;
    role: string;
}
export interface AuthResponse {
    user: {
        id: string;
        email: string;
        username: string;
        role: string;
    };
    token: string;
}
export declare class AuthService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    register(registerDto: RegisterDto): Promise<AuthResponse>;
    login(loginDto: LoginDto): Promise<AuthResponse>;
    validateToken(payload: JwtPayload): Promise<User>;
    private generateToken;
}
