"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ARMOR = void 0;
const enums_1 = require("../enums");
exports.ARMOR = [
    {
        id: 'vault-jumpsuit',
        name: 'Комбинезон Убежища',
        description: 'Стандартная одежда жителей Убежища. Удобная, но не обеспечивает защиты.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 1.0,
        value: 50,
        armorType: 'clothing',
        armorClass: 'jumpsuit',
        equipmentSlots: ['full_body'],
        stats: {
            protection: {
                damageThreshold: 0,
                damageResistance: 5,
                resistances: {
                    normal: 5,
                    laser: 0,
                    plasma: 0,
                    explosive: 0,
                    fire: 0,
                    electric: 0,
                    poison: 0,
                    radiation: 10,
                    acid: 0,
                    cold: 5
                },
                environmentalProtection: {
                    radiation: 10,
                    temperature: 15,
                    pressure: 0,
                    vacuum: false,
                    underwater: false,
                    chemical: 5
                }
            },
            weight: 1.0,
            encumbrance: 0,
            durability: 80,
            maxDurability: 100,
            degradationRate: 0.1,
            statModifiers: {},
            skillModifiers: {},
            requirements: {
                strengthRequired: 1,
                enduranceRequired: 1
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: false,
                communicationSystem: false,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: [],
        installedMods: [],
        maxMods: 0,
        specialAbilities: [],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'cloth': 8, 'thread': 4 },
            skill: { 'repair': 15 },
            tools: ['sewing_kit']
        },
        canBeRepaired: true,
        repairCost: { 'cloth': 2, 'thread': 1 },
        repairSkill: 'repair',
        environmentalEffects: {
            heatSignature: 50,
            noiseLevel: 10,
            visibility: 50,
            weatherProtection: false,
            temperatureRange: { min: -10, max: 40 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 168
        },
        manufacturer: 'Vault-Tec',
        model: 'Standard Issue',
        yearMade: 2077,
        loreText: 'Каждый житель Убежища получает такой комбинезон при рождении.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    },
    {
        id: 'raider-armor',
        name: 'Броня рейдера',
        description: 'Грубо сколоченная броня из кусков металла, кожи и мусора. Чаще всего используется мародёрами и рейдерами.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 9.0,
        value: 120,
        armorType: 'light',
        armorClass: 'leather',
        equipmentSlots: ['torso', 'arms'],
        stats: {
            protection: {
                damageThreshold: 1,
                damageResistance: 10,
                resistances: {
                    normal: 10,
                    laser: 3,
                    plasma: 2,
                    explosive: 5,
                    fire: 4,
                    electric: 0,
                    poison: 3,
                    radiation: 0,
                    acid: 1,
                    cold: 8
                },
                environmentalProtection: {
                    radiation: 0,
                    temperature: 10,
                    pressure: 0,
                    vacuum: false,
                    underwater: false,
                    chemical: 2
                }
            },
            weight: 9.0,
            encumbrance: 6,
            durability: 40,
            maxDurability: 60,
            degradationRate: 0.7,
            statModifiers: {
                agilityPenalty: -2
            },
            skillModifiers: {
                sneakPenalty: -15
            },
            requirements: {
                strengthRequired: 2,
                enduranceRequired: 2
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: false,
                communicationSystem: false,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: ['plating'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: [],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'leather': 8, 'metal-parts': 3, 'thread': 4 },
            skill: { 'repair': 20 },
            tools: ['workbench']
        },
        canBeRepaired: true,
        repairCost: { 'leather': 2, 'metal-parts': 1 },
        repairSkill: 'repair',
        environmentalEffects: {
            heatSignature: 75,
            noiseLevel: 30,
            visibility: 80,
            weatherProtection: false,
            temperatureRange: { min: -10, max: 35 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 48
        },
        loreText: 'Броня, собранная из всего, что попалось под руку.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    },
    {
        id: 'leather-armor',
        name: 'Кожаная броня',
        description: 'Самодельная броня из кожи и металлических пластин. Популярна среди рейдеров.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.COMMON,
        weight: 8.0,
        value: 200,
        armorType: 'light',
        armorClass: 'leather',
        equipmentSlots: ['torso', 'arms'],
        stats: {
            protection: {
                damageThreshold: 2,
                damageResistance: 15,
                resistances: {
                    normal: 15,
                    laser: 5,
                    plasma: 5,
                    explosive: 10,
                    fire: 8,
                    electric: 0,
                    poison: 5,
                    radiation: 0,
                    acid: 3,
                    cold: 12
                },
                environmentalProtection: {
                    radiation: 0,
                    temperature: 20,
                    pressure: 0,
                    vacuum: false,
                    underwater: false,
                    chemical: 5
                }
            },
            weight: 8.0,
            encumbrance: 5,
            durability: 60,
            maxDurability: 80,
            degradationRate: 0.5,
            statModifiers: {
                agilityPenalty: -1
            },
            skillModifiers: {
                sneakPenalty: -10
            },
            requirements: {
                strengthRequired: 3,
                enduranceRequired: 3
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: false,
                communicationSystem: false,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: ['plating', 'lining'],
        installedMods: [],
        maxMods: 2,
        specialAbilities: [],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'leather': 12, 'metal-parts': 4, 'thread': 6 },
            skill: { 'repair': 35 },
            tools: ['workbench']
        },
        canBeRepaired: true,
        repairCost: { 'leather': 3, 'metal-parts': 1 },
        repairSkill: 'repair',
        environmentalEffects: {
            heatSignature: 60,
            noiseLevel: 20,
            visibility: 70,
            weatherProtection: true,
            temperatureRange: { min: -20, max: 45 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 72
        },
        loreText: 'Простая, но эффективная защита для выживания в пустоши.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    },
    {
        id: 'metal-armor',
        name: 'Металлическая броня',
        description: 'Самодельная броня из металлических пластин. Тяжелая, но надежная.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 15.0,
        value: 500,
        armorType: 'medium',
        armorClass: 'metal',
        equipmentSlots: ['torso', 'arms', 'legs'],
        stats: {
            protection: {
                damageThreshold: 5,
                damageResistance: 25,
                resistances: {
                    normal: 25,
                    laser: 15,
                    plasma: 10,
                    explosive: 20,
                    fire: 15,
                    electric: 5,
                    poison: 0,
                    radiation: 0,
                    acid: 8,
                    cold: 20
                },
                environmentalProtection: {
                    radiation: 5,
                    temperature: 25,
                    pressure: 10,
                    vacuum: false,
                    underwater: false,
                    chemical: 10
                }
            },
            weight: 15.0,
            encumbrance: 15,
            durability: 80,
            maxDurability: 120,
            degradationRate: 0.3,
            statModifiers: {
                agilityPenalty: -2,
                strengthBonus: 1
            },
            skillModifiers: {
                sneakPenalty: -25,
                repairBonus: 5
            },
            requirements: {
                strengthRequired: 5,
                enduranceRequired: 4
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: false,
                communicationSystem: false,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: ['plating', 'lining', 'servos'],
        installedMods: [],
        maxMods: 3,
        specialAbilities: [],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'metal-parts': 20, 'screws': 12, 'leather': 6 },
            skill: { 'repair': 50 },
            tools: ['workbench']
        },
        canBeRepaired: true,
        repairCost: { 'metal-parts': 4, 'screws': 2 },
        repairSkill: 'repair',
        environmentalEffects: {
            heatSignature: 80,
            noiseLevel: 40,
            visibility: 90,
            weatherProtection: true,
            temperatureRange: { min: -30, max: 50 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 96
        },
        loreText: 'Тяжелая броня из металлолома. Защищает от большинства угроз пустоши.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    },
    {
        id: 'combat-armor',
        name: 'Боевая броня',
        description: 'Довоенная военная броня. Обеспечивает отличную защиту при сохранении мобильности.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.RARE,
        weight: 18.0,
        value: 1500,
        armorType: 'medium',
        armorClass: 'combat',
        equipmentSlots: ['torso', 'arms', 'legs'],
        stats: {
            protection: {
                damageThreshold: 8,
                damageResistance: 35,
                resistances: {
                    normal: 35,
                    laser: 25,
                    plasma: 20,
                    explosive: 30,
                    fire: 25,
                    electric: 15,
                    poison: 10,
                    radiation: 15,
                    acid: 20,
                    cold: 30
                },
                environmentalProtection: {
                    radiation: 15,
                    temperature: 35,
                    pressure: 20,
                    vacuum: false,
                    underwater: false,
                    chemical: 25
                }
            },
            weight: 18.0,
            encumbrance: 10,
            durability: 120,
            maxDurability: 150,
            degradationRate: 0.2,
            statModifiers: {
                agilityPenalty: -1,
                strengthBonus: 1,
                enduranceBonus: 1
            },
            skillModifiers: {
                sneakPenalty: -15,
                repairBonus: 10
            },
            requirements: {
                strengthRequired: 6,
                enduranceRequired: 5
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: false,
                communicationSystem: true,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: ['plating', 'lining', 'servos', 'helmet_mod'],
        installedMods: [],
        maxMods: 4,
        specialAbilities: ['tactical_awareness'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 6, 'electronic_parts': 2, 'screws': 4 },
        repairSkill: 'repair',
        environmentalEffects: {
            heatSignature: 70,
            noiseLevel: 30,
            visibility: 80,
            weatherProtection: true,
            temperatureRange: { min: -40, max: 60 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 120
        },
        manufacturer: 'US Army',
        model: 'Mark II',
        yearMade: 2076,
        militaryDesignation: 'M-199',
        loreText: 'Стандартная броня американской армии времен Великой войны.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    },
    {
        id: 'power-armor-t45d',
        name: 'Силовая броня T-45d',
        description: 'Ранняя модель силовой брони. Требует обучения и источник питания.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.EPIC,
        weight: 45.0,
        value: 8000,
        armorType: 'power_armor',
        armorClass: 'power',
        equipmentSlots: ['full_body'],
        stats: {
            protection: {
                damageThreshold: 15,
                damageResistance: 50,
                resistances: {
                    normal: 50,
                    laser: 40,
                    plasma: 35,
                    explosive: 45,
                    fire: 40,
                    electric: 30,
                    poison: 100,
                    radiation: 80,
                    acid: 35,
                    cold: 60
                },
                environmentalProtection: {
                    radiation: 80,
                    temperature: 90,
                    pressure: 100,
                    vacuum: true,
                    underwater: true,
                    chemical: 100
                }
            },
            weight: 45.0,
            encumbrance: -10,
            durability: 200,
            maxDurability: 250,
            degradationRate: 0.8,
            statModifiers: {
                strengthBonus: 3,
                enduranceBonus: 2,
                agilityPenalty: -2
            },
            skillModifiers: {
                sneakPenalty: -50,
                repairBonus: 15,
                scienceBonus: 10
            },
            requirements: {
                strengthRequired: 7,
                enduranceRequired: 6,
                skillRequired: { 'power_armor': 50 }
            },
            specialProperties: {
                stealthDetection: true,
                nightVision: true,
                thermalVision: false,
                breathingApparatus: true,
                communicationSystem: true,
                targetingSystem: true,
                jumpJets: false,
                magneticBoots: false
            }
        },
        powerSystem: {
            hasPowerSystem: true,
            maxPower: 1000,
            currentPower: 1000,
            powerConsumption: 10,
            powerSources: ['fusion_core', 'energy_cell'],
            batteryLife: 100
        },
        modificationSlots: ['plating', 'servos', 'power_system', 'helmet_mod'],
        installedMods: [],
        maxMods: 4,
        specialAbilities: ['power_slam', 'enhanced_strength'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 15, 'electronic_parts': 8, 'fusion_core': 1 },
        repairSkill: 'science',
        environmentalEffects: {
            heatSignature: 120,
            noiseLevel: 80,
            visibility: 150,
            weatherProtection: true,
            temperatureRange: { min: -100, max: 200 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 200
        },
        manufacturer: 'West Tek',
        model: 'T-45d',
        yearMade: 2067,
        militaryDesignation: 'T-45d Power Armor',
        loreText: 'Первая серийная силовая броня. Революция в военном деле.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: false,
        requiresTraining: true,
        isRestricted: true,
        createdAt: new Date(),
    },
    {
        id: 'power-armor-t51b',
        name: 'Силовая броня T-51b',
        description: 'Улучшенная модель силовой брони. Вершина довоенных технологий.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.LEGENDARY,
        weight: 40.0,
        value: 15000,
        armorType: 'power_armor',
        armorClass: 'power',
        equipmentSlots: ['full_body'],
        stats: {
            protection: {
                damageThreshold: 20,
                damageResistance: 60,
                resistances: {
                    normal: 60,
                    laser: 50,
                    plasma: 45,
                    explosive: 55,
                    fire: 50,
                    electric: 40,
                    poison: 100,
                    radiation: 95,
                    acid: 45,
                    cold: 70
                },
                environmentalProtection: {
                    radiation: 95,
                    temperature: 95,
                    pressure: 100,
                    vacuum: true,
                    underwater: true,
                    chemical: 100
                }
            },
            weight: 40.0,
            encumbrance: -15,
            durability: 250,
            maxDurability: 300,
            degradationRate: 0.6,
            statModifiers: {
                strengthBonus: 4,
                enduranceBonus: 3,
                agilityPenalty: -1
            },
            skillModifiers: {
                sneakPenalty: -40,
                repairBonus: 20,
                scienceBonus: 15
            },
            requirements: {
                strengthRequired: 8,
                enduranceRequired: 7,
                skillRequired: { 'power_armor': 75 }
            },
            specialProperties: {
                stealthDetection: true,
                nightVision: true,
                thermalVision: true,
                breathingApparatus: true,
                communicationSystem: true,
                targetingSystem: true,
                jumpJets: false,
                magneticBoots: true
            }
        },
        powerSystem: {
            hasPowerSystem: true,
            maxPower: 1500,
            currentPower: 1500,
            powerConsumption: 8,
            powerSources: ['fusion_core', 'energy_cell'],
            batteryLife: 150
        },
        modificationSlots: ['plating', 'servos', 'power_system', 'helmet_mod', 'torso_mod'],
        installedMods: [],
        maxMods: 5,
        specialAbilities: ['power_slam', 'enhanced_strength', 'radiation_immunity'],
        canBeCrafted: false,
        canBeRepaired: true,
        repairCost: { 'metal-parts': 20, 'electronic_parts': 12, 'fusion_core': 2, 'rare_metals': 3 },
        repairSkill: 'science',
        setInfo: {
            setId: 't51b-set',
            setName: 'Комплект T-51b',
            setPieces: ['power-armor-t51b', 't51b-helmet'],
            setBonuses: [
                {
                    piecesRequired: 2,
                    bonus: {
                        statBonus: { 'strength': 1, 'endurance': 1 },
                        resistanceBonus: { 'radiation': 10 },
                        specialAbility: 'advanced_hud'
                    }
                }
            ]
        },
        environmentalEffects: {
            heatSignature: 100,
            noiseLevel: 60,
            visibility: 120,
            weatherProtection: true,
            temperatureRange: { min: -150, max: 300 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 300
        },
        manufacturer: 'West Tek',
        model: 'T-51b',
        yearMade: 2076,
        militaryDesignation: 'T-51b Advanced Power Armor',
        loreText: 'Самая совершенная силовая броня довоенной эпохи. Символ американской мощи.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: false,
        requiresTraining: true,
        isRestricted: true,
        createdAt: new Date(),
    },
    {
        id: 'hazmat-suit',
        name: 'Защитный костюм',
        description: 'Герметичный костюм для работы в опасных условиях. Защищает от радиации и химикатов.',
        type: enums_1.ItemType.ARMOR,
        rarity: enums_1.ItemRarity.UNCOMMON,
        weight: 5.0,
        value: 800,
        armorType: 'environmental_suit',
        armorClass: 'hazmat',
        equipmentSlots: ['full_body'],
        stats: {
            protection: {
                damageThreshold: 1,
                damageResistance: 10,
                resistances: {
                    normal: 10,
                    laser: 5,
                    plasma: 5,
                    explosive: 5,
                    fire: 20,
                    electric: 15,
                    poison: 90,
                    radiation: 95,
                    acid: 80,
                    cold: 30
                },
                environmentalProtection: {
                    radiation: 95,
                    temperature: 60,
                    pressure: 50,
                    vacuum: false,
                    underwater: false,
                    chemical: 95
                }
            },
            weight: 5.0,
            encumbrance: 8,
            durability: 40,
            maxDurability: 60,
            degradationRate: 1.0,
            statModifiers: {
                agilityPenalty: -2,
                perceptionPenalty: -1
            },
            skillModifiers: {
                sneakPenalty: -20,
                scienceBonus: 10,
                medicineBonus: 15
            },
            requirements: {
                strengthRequired: 4,
                enduranceRequired: 3
            },
            specialProperties: {
                stealthDetection: false,
                nightVision: false,
                thermalVision: false,
                breathingApparatus: true,
                communicationSystem: false,
                targetingSystem: false,
                jumpJets: false,
                magneticBoots: false
            }
        },
        modificationSlots: ['lining'],
        installedMods: [],
        maxMods: 1,
        specialAbilities: ['radiation_immunity', 'chemical_immunity'],
        canBeCrafted: true,
        craftingRecipe: {
            components: { 'plastic': 15, 'rubber': 8, 'electronic_parts': 3 },
            skill: { 'science': 60, 'repair': 40 },
            tools: ['chemistry_set']
        },
        canBeRepaired: true,
        repairCost: { 'plastic': 3, 'rubber': 2 },
        repairSkill: 'science',
        environmentalEffects: {
            heatSignature: 40,
            noiseLevel: 25,
            visibility: 80,
            weatherProtection: true,
            temperatureRange: { min: -50, max: 80 }
        },
        condition: {
            currentCondition: 100,
            maintenanceRequired: false,
            maintenanceInterval: 48
        },
        manufacturer: 'RobCo Industries',
        model: 'Environmental Protection Suit',
        yearMade: 2077,
        loreText: 'Спасение для тех, кто работает в самых опасных зонах пустоши.',
        isUnique: false,
        isQuestItem: false,
        canBeSold: true,
        canBeDropped: true,
        canBeStolen: true,
        requiresTraining: false,
        isRestricted: false,
        createdAt: new Date(),
    }
];
//# sourceMappingURL=armor.js.map