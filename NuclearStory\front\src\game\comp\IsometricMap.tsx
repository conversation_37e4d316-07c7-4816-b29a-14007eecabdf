
import React, { useRef, useEffect, useState } from 'react'
import styles from './IsometricMap.module.css'
import { WorldMap } from '../../shared/types/World'

interface IsometricMapProps {
  width?: number
  height?: number
  worlds: WorldMap[]
  currentWorld: WorldMap | null
}

const IsometricMap: React.FC<IsometricMapProps> = ({ 
  width = 1920, 
  height = 1080,
  worlds,
  currentWorld
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [cameraX, setCameraX] = useState(0)
  const [cameraY, setCameraY] = useState(0)
  const [zoom, setZoom] = useState(1.0)

  // Константы для изометрической проекции
  const BASE_TILE_WIDTH = 80
  const BASE_TILE_HEIGHT = 40
  const MAP_SIZE = 100

  // Масштабированные размеры тайлов
  const TILE_WIDTH = BASE_TILE_WIDTH * zoom
  const TILE_HEIGHT = BASE_TILE_HEIGHT * zoom
  
  // currentWorld теперь приходит через пропсы
  
  // Функция преобразования изометрических координат в экранные
  const isoToScreen = (isoX: number, isoY: number) => {
    const screenX = (isoX - isoY) * (TILE_WIDTH / 2)
    const screenY = (isoX + isoY) * (TILE_HEIGHT / 2)
    return { x: screenX, y: screenY }
  }
  
  // Функция преобразования экранных координат в изометрические
  const screenToIso = (screenX: number, screenY: number) => {
    const isoX = (screenX / (TILE_WIDTH / 2) + screenY / (TILE_HEIGHT / 2)) / 2
    const isoY = (screenY / (TILE_HEIGHT / 2) - screenX / (TILE_WIDTH / 2)) / 2
    return { x: Math.floor(isoX), y: Math.floor(isoY) }
  }
  
  // Функция отрисовки ромбовидного тайла
  const drawTile = (ctx: CanvasRenderingContext2D, screenX: number, screenY: number, isoX: number, isoY: number) => {
    const centerX = screenX + width / 2 - cameraX
    const centerY = screenY + height / 2 - cameraY
    
    // Проверяем, находится ли тайл в видимой области
    if (centerX < -TILE_WIDTH || centerX > width + TILE_WIDTH || 
        centerY < -TILE_HEIGHT || centerY > height + TILE_HEIGHT) {
      return
    }
    
    // Рисуем ромб
    ctx.beginPath()
    ctx.moveTo(centerX, centerY - TILE_HEIGHT / 2) // Верх
    ctx.lineTo(centerX + TILE_WIDTH / 2, centerY) // Право
    ctx.lineTo(centerX, centerY + TILE_HEIGHT / 2) // Низ
    ctx.lineTo(centerX - TILE_WIDTH / 2, centerY) // Лево
    ctx.closePath()
    
    // Заливка (прозрачная)
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    ctx.fill()
    
    // Обводка
    ctx.strokeStyle = '#444'
    ctx.lineWidth = 1
    ctx.stroke()
    
    // Координаты для отладки (опционально)
    if (TILE_WIDTH >= 60) { // Показываем координаты только если тайлы достаточно большие
      ctx.fillStyle = '#666'
      ctx.font = '10px monospace'
      ctx.textAlign = 'center'
      ctx.fillText(`${isoX},${isoY}`, centerX, centerY + 3)
    }
  }
  
  // Основная функция отрисовки
  const draw = () => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    // Очищаем канвас
    ctx.clearRect(0, 0, width, height)
    
    // Рисуем сетку тайлов
    for (let isoY = 0; isoY < MAP_SIZE; isoY++) {
      for (let isoX = 0; isoX < MAP_SIZE; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY)
        drawTile(ctx, screenX, screenY, isoX, isoY)
      }
    }
    
    // Рисуем центральную точку для ориентации
    ctx.fillStyle = '#ff353500'
    ctx.beginPath()
    ctx.arc(width / 2, height / 2, 3, 0, 2 * Math.PI)
    ctx.fill()
  }
  
  // Обработчик движения мыши для перемещения камеры
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (e.buttons === 1) { // Левая кнопка мыши зажата
      setCameraX(prev => prev - e.movementX)
      setCameraY(prev => prev - e.movementY)
    }
  }
  
  // Обработчик изменения зума
  const handleZoomChange = (newZoom: number) => {
    setZoom(Math.max(0.5, Math.min(2.0, newZoom)))
  }

  // Обработчик колеса мыши для зума
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault()
    const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1
    handleZoomChange(zoom + zoomDelta)
  }
  
  // Обработчик клика для получения координат
  const handleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return
    
    const clickX = e.clientX - rect.left - width / 2 + cameraX
    const clickY = e.clientY - rect.top - height / 2 + cameraY
    
    const { x: isoX, y: isoY } = screenToIso(clickX, clickY)
    
    if (isoX >= 0 && isoX < MAP_SIZE && isoY >= 0 && isoY < MAP_SIZE) {
      console.log(`Clicked tile: (${isoX}, ${isoY})`)
      // Здесь можно добавить логику обработки клика по тайлу
    }
  }
  
  // Обработчик клавиш для управления камерой
  const handleKeyDown = (e: KeyboardEvent) => {
    const moveSpeed = 20
    switch (e.key) {
      case 'ArrowUp':
        setCameraY(prev => prev - moveSpeed)
        e.preventDefault()
        break
      case 'ArrowDown':
        setCameraY(prev => prev + moveSpeed)
        e.preventDefault()
        break
      case 'ArrowLeft':
        setCameraX(prev => prev - moveSpeed)
        e.preventDefault()
        break
      case 'ArrowRight':
        setCameraX(prev => prev + moveSpeed)
        e.preventDefault()
        break
    }
  }

  // Эффект для добавления обработчика клавиш
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  // Эффект для перерисовки при изменении камеры или зума
  useEffect(() => {
    draw()
  }, [cameraX, cameraY, zoom, currentWorld])

  // Начальная отрисовка
  useEffect(() => {
    draw()
  }, [])
  
  return (
    <div className={styles.mapContainer}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove}
        onWheel={handleWheel}
        onClick={handleClick}
        style={{ cursor: 'grab' }}
      />
      <div className={styles.mapControls}>
        <div className={styles.coordinates}>
          Камера: ({Math.round(cameraX)}, {Math.round(cameraY)})
        </div>
        <div className={styles.coordinates}>
          Зум: {zoom.toFixed(1)}x
        </div>
        <div className={styles.instructions}>
          ЛКМ - перетаскивание, стрелки - движение, колесо - зум
        </div>
      </div>
      <div className={styles.zoomControls}>
        <label className={styles.zoomLabel}>
          Зум: {zoom.toFixed(1)}x
        </label>
        <input
          type="range"
          min="0.5"
          max="2.0"
          step="0.1"
          value={zoom}
          onChange={(e) => handleZoomChange(parseFloat(e.target.value))}
          className={styles.zoomSlider}
        />
      </div>
    </div>
  )
}

export default IsometricMap
