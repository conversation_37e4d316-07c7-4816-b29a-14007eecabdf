import { create } from 'zustand'
import { WorldMap } from '../shared/types/World'

interface GameSettings {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: 'easy' | 'normal' | 'hard'
  autoSave: boolean
}

interface GameStoreState {
  worlds: WorldMap[]
  currentWorld: WorldMap | null
  currentWorldId: string | null
  settings: GameSettings
  setWorlds: (worlds: WorldMap[]) => void
  setCurrentWorld: (world: WorldMap | null) => void
  setCurrentWorldId: (worldId: string | null) => void
  updateSettings: (newSettings: Partial<GameSettings>) => void
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameStoreState>((set) => ({
  worlds: [],
  currentWorld: null,
  currentWorldId: null,
  settings: defaultSettings,
  setWorlds: (worlds: WorldMap[]) => set({ worlds }),
  setCurrentWorld: (world: WorldMap | null) => set({ currentWorld: world }),
  setCurrentWorldId: (worldId: string | null) => set({ currentWorldId: worldId }),
  updateSettings: (newSettings: Partial<GameSettings>) =>
    set((state) => ({
      settings: { ...state.settings, ...newSettings }
    }))
}))
