import { create } from 'zustand'
import { WorldMap } from '../shared/types/World'

interface GameStoreState {
  worlds: WorldMap[]
  currentWorld: WorldMap | null
  setWorlds: (worlds: WorldMap[]) => void
  setCurrentWorld: (world: WorldMap | null) => void
}

export const useGameStore = create<GameStoreState>((set) => ({
  worlds: [],
  currentWorld: null,
  setWorlds: (worlds: WorldMap[]) => set({ worlds }),
  setCurrentWorld: (world: WorldMap | null) => set({ currentWorld: world })
}))
