import { LocationType } from '../enums';
import { Position } from './Player';
export interface StructureResource {
    type: string;
    amount: number;
    maxAmount: number;
    regenerationRate?: number;
}
export interface StructureUpgrade {
    id: string;
    name: string;
    description: string;
    cost: Record<string, number>;
    requirements: string[];
    isUnlocked: boolean;
    isBuilt: boolean;
}
export interface Structure {
    id: string;
    name: string;
    description: string;
    type: LocationType;
    position: Position;
    size: {
        width: number;
        height: number;
    };
    level: number;
    maxLevel: number;
    durability: number;
    maxDurability: number;
    resources: StructureResource[];
    storageCapacity: number;
    currentStorage: number;
    availableUpgrades: StructureUpgrade[];
    completedUpgrades: string[];
    maxPopulation: number;
    currentPopulation: number;
    workers: string[];
    defenseRating: number;
    securityLevel: number;
    isUnderAttack: boolean;
    powerConsumption: number;
    powerGeneration: number;
    hasWater: boolean;
    hasElectricity: boolean;
    ownerId?: string;
    factionId?: string;
    isPublic: boolean;
    accessLevel: 'public' | 'faction' | 'private';
    connectedStructures: string[];
    tradeRoutes: string[];
    createdAt: Date;
    lastUpdatedAt: Date;
    lastVisitedAt?: Date;
}
