"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateId = generateId;
exports.generatePlayerId = generatePlayerId;
exports.generateObjectId = generateObjectId;
exports.generateLocationId = generateLocationId;
exports.generateQuestId = generateQuestId;
exports.generateUUID = generateUUID;
function generateId(prefix = 'id') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${prefix}_${timestamp}_${random}`;
}
function generatePlayerId() {
    return generateId('player');
}
function generateObjectId() {
    return generateId('obj');
}
function generateLocationId() {
    return generateId('loc');
}
function generateQuestId() {
    return generateId('quest');
}
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
//# sourceMappingURL=generateId.js.map