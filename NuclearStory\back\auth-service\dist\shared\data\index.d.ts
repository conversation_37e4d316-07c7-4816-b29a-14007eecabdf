export * from './weapons';
export * from './armor';
export * from './items';
export declare const GAME_DATA: {
    weapons: import("..").Weapon[];
    armor: import("..").Armor[];
    items: import("..").InventoryItem[];
    consumables: import("..").InventoryItem[];
    tools: import("..").InventoryItem[];
    resources: import("..").InventoryItem[];
};
export declare const findWeaponById: (id: string) => import("..").Weapon;
export declare const findArmorById: (id: string) => import("..").Armor;
export declare const findItemById: (id: string) => import("..").InventoryItem;
export declare const getWeaponsByRarity: (rarity: string) => import("..").Weapon[];
export declare const getArmorByRarity: (rarity: string) => import("..").Armor[];
export declare const getItemsByRarity: (rarity: string) => import("..").InventoryItem[];
export declare const getWeaponsByType: (weaponType: string) => import("..").Weapon[];
export declare const getArmorByType: (armorType: string) => import("..").Armor[];
export declare const GAME_STATS: {
    totalWeapons: number;
    totalArmor: number;
    totalItems: number;
    totalConsumables: number;
    totalTools: number;
    totalResources: number;
};
