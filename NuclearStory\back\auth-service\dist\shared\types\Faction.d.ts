import { FactionAlignment } from '../enums';
export interface FactionRank {
    id: string;
    name: string;
    description: string;
    level: number;
    requirements: {
        reputation: number;
        completedQuests?: string[];
        skills?: Record<string, number>;
        items?: string[];
    };
    benefits: {
        discountPercentage?: number;
        accessToAreas?: string[];
        specialItems?: string[];
        abilities?: string[];
    };
}
export interface FactionTerritory {
    locationIds: string[];
    influenceMap: Record<string, number>;
    borderLocations: string[];
    strategicLocations: string[];
}
export interface FactionRelationship {
    factionId: string;
    relationshipType: 'allied' | 'friendly' | 'neutral' | 'hostile' | 'enemy';
    relationshipValue: number;
    isAtWar: boolean;
    tradeAgreement: boolean;
    nonAggressionPact: boolean;
    lastConflictAt?: Date;
}
export interface FactionQuest {
    questId: string;
    isRepeatable: boolean;
    minimumRank: number;
    reputationReward: number;
    isAvailable: boolean;
}
export interface FactionResource {
    type: string;
    amount: number;
    productionRate: number;
    consumptionRate: number;
    tradable: boolean;
}
export interface Faction {
    id: string;
    name: string;
    description: string;
    shortName: string;
    alignment: FactionAlignment;
    ideology: string;
    goals: string[];
    leaderId?: string;
    leaderTitle: string;
    governmentType: 'democracy' | 'autocracy' | 'oligarchy' | 'anarchy' | 'technocracy';
    ranks: FactionRank[];
    memberCount: number;
    recruitmentOpen: boolean;
    recruitmentRequirements?: {
        minimumLevel?: number;
        skills?: Record<string, number>;
        items?: string[];
        questsCompleted?: string[];
    };
    territory: FactionTerritory;
    totalInfluence: number;
    isExpanding: boolean;
    relationships: FactionRelationship[];
    playerReputation: number;
    playerRank?: string;
    playerJoinedAt?: Date;
    resources: FactionResource[];
    currency: number;
    tradeRoutes: string[];
    militaryStrength: number;
    defenseRating: number;
    aggressionLevel: number;
    availableQuests: FactionQuest[];
    technologyLevel: number;
    specialAbilities: string[];
    uniqueItems: string[];
    colors: {
        primary: string;
        secondary: string;
    };
    symbol?: string;
    uniform?: string;
    architecture?: string;
    foundedYear?: number;
    founderName?: string;
    history: string;
    motto?: string;
    isActive: boolean;
    isPlayable: boolean;
    isHostileToPlayer: boolean;
    isEssential: boolean;
    currentEvents: string[];
    scheduledEvents: {
        eventId: string;
        scheduledAt: Date;
    }[];
    createdAt: Date;
    lastActivityAt: Date;
    lastWarAt?: Date;
}
