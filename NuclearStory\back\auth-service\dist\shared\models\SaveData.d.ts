import { GameStatus } from '../enums';
import { GameStats, Coordinates } from '../types';
import { Quest } from './Quest';
import { PlayerInventoryItem } from './InventoryItem';
export declare class GameLocation {
    id: string;
    name: string;
    coordinates?: Coordinates;
    description?: string;
    discoveredAreas?: string[];
    constructor(partial?: Partial<GameLocation>);
}
export declare class GameProgress {
    level: number;
    experience: number;
    experienceToNext: number;
    completedQuests: string[];
    discoveredLocations: string[];
    unlockedAchievements: string[];
    storyFlags?: string[];
    constructor(partial?: Partial<GameProgress>);
}
export declare class SaveData {
    id: string;
    userId: string;
    saveName: string;
    description?: string;
    worldId: string;
    gameStatus: GameStatus;
    gameStats: GameStats;
    progress: GameProgress;
    currentLocationId: string;
    currentPosition?: Coordinates;
    activeQuests: Quest[];
    inventory: PlayerInventoryItem[];
    playtimeMinutes: number;
    createdAt: Date;
    updatedAt: Date;
    lastPlayedAt?: Date;
    screenshot?: string;
    constructor(partial?: Partial<SaveData>);
}
export declare class CreateSaveDataDto {
    saveName: string;
    description?: string;
    gameStats?: GameStats;
    currentLocationId?: string;
}
export declare class UpdateSaveDataDto {
    saveName?: string;
    description?: string;
    gameStatus?: GameStatus;
    gameStats?: GameStats;
    progress?: GameProgress;
    currentLocationId?: string;
    currentPosition?: Coordinates;
    activeQuests?: Quest[];
    inventory?: PlayerInventoryItem[];
    playtimeMinutes?: number;
    screenshot?: string;
}
