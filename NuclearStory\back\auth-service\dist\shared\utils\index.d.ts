import { GameStats } from '../types';
export declare class ValidationUtils {
    static isValidEmail(email: string): boolean;
    static isValidUsername(username: string): boolean;
    static isValidPassword(password: string): boolean;
}
export declare class GameUtils {
    static clampStat(value: number, min?: number, max?: number): number;
    static applyStatChanges(current: GameStats, changes: Partial<GameStats>): GameStats;
    static calculateSurvivalScore(stats: GameStats): number;
    static isPlayerInDanger(stats: GameStats): boolean;
}
export declare class DateUtils {
    static formatPlaytime(minutes: number): string;
    static getTimeSince(date: Date): string;
}
export declare class Constants {
    static readonly DEFAULT_GAME_STATS: GameStats;
    static readonly MAX_INVENTORY_SLOTS = 50;
    static readonly MAX_SAVE_SLOTS = 10;
    static readonly DEFAULT_LOCATION = "bunker";
    static readonly STAT_DECAY_RATES: {
        hunger: number;
        thirst: number;
        energy: number;
    };
    static readonly CRITICAL_THRESHOLDS: {
        health: number;
        hunger: number;
        thirst: number;
        radiation: number;
        energy: number;
    };
}
export * from './diceRoll';
export * from './generateId';
