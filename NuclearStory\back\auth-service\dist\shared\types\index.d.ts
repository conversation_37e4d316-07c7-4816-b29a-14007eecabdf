import { GameStatus, EventType } from '../enums';
export * from './Common';
export { Position as Coordinates } from './Common';
export interface GameStats {
    health: number;
    maxHealth: number;
    energy: number;
    maxEnergy: number;
    hunger: number;
    thirst: number;
    radiation: number;
    sanity?: number;
}
export interface Choice {
    id: string;
    text: string;
    consequences?: {
        stats?: Partial<GameStats>;
        items?: string[];
        unlockQuests?: string[];
        unlockLocations?: string[];
    };
}
export interface StoryEvent {
    id: string;
    type: EventType;
    title: string;
    description: string;
    choices?: Choice[];
    requirements?: {
        level?: number;
        items?: string[];
        completedQuests?: string[];
        stats?: Partial<GameStats>;
    };
}
export interface GameSession {
    id: string;
    userId: string;
    status: GameStatus;
    currentLocation: string;
    gameStats: GameStats;
    startedAt: Date;
    lastPlayedAt: Date;
    playtimeMinutes: number;
}
export * from './NPC';
export * from './Location';
export * from './LocationGrid';
export * from './MapCell';
export * from './Unit';
export * from './Weapon';
export * from './Armor';
export * from './Faction';
export * from './Event';
export * from './World';
export * from './Door';
export * from './Container';
