{"hash": "d14d12fc", "browserHash": "4eff7a70", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "391d4145", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "117f906f", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "67bc75ae", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "831b7890", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "f9745bd4", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "460d7769", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c3f6b8e3", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "0354ea3b", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "68601f42", "needsInterop": false}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}