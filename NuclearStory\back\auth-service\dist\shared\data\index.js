"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GAME_STATS = exports.getArmorByType = exports.getWeaponsByType = exports.getItemsByRarity = exports.getArmorByRarity = exports.getWeaponsByRarity = exports.findItemById = exports.findArmorById = exports.findWeaponById = exports.GAME_DATA = void 0;
__exportStar(require("./weapons"), exports);
__exportStar(require("./armor"), exports);
__exportStar(require("./items"), exports);
const weapons_1 = require("./weapons");
const armor_1 = require("./armor");
const items_1 = require("./items");
exports.GAME_DATA = {
    weapons: weapons_1.WEAPONS,
    armor: armor_1.ARMOR,
    items: items_1.ALL_ITEMS,
    consumables: items_1.CONSUMABLES,
    tools: items_1.TOOLS,
    resources: items_1.RESOURCES
};
const findWeaponById = (id) => weapons_1.WEAPONS.find(weapon => weapon.id === id);
exports.findWeaponById = findWeaponById;
const findArmorById = (id) => armor_1.ARMOR.find(armor => armor.id === id);
exports.findArmorById = findArmorById;
const findItemById = (id) => items_1.ALL_ITEMS.find(item => item.id === id);
exports.findItemById = findItemById;
const getWeaponsByRarity = (rarity) => weapons_1.WEAPONS.filter(weapon => weapon.rarity === rarity);
exports.getWeaponsByRarity = getWeaponsByRarity;
const getArmorByRarity = (rarity) => armor_1.ARMOR.filter(armor => armor.rarity === rarity);
exports.getArmorByRarity = getArmorByRarity;
const getItemsByRarity = (rarity) => items_1.ALL_ITEMS.filter(item => item.rarity === rarity);
exports.getItemsByRarity = getItemsByRarity;
const getWeaponsByType = (weaponType) => weapons_1.WEAPONS.filter(weapon => weapon.weaponType === weaponType);
exports.getWeaponsByType = getWeaponsByType;
const getArmorByType = (armorType) => armor_1.ARMOR.filter(armor => armor.armorType === armorType);
exports.getArmorByType = getArmorByType;
exports.GAME_STATS = {
    totalWeapons: weapons_1.WEAPONS.length,
    totalArmor: armor_1.ARMOR.length,
    totalItems: items_1.ALL_ITEMS.length,
    totalConsumables: items_1.CONSUMABLES.length,
    totalTools: items_1.TOOLS.length,
    totalResources: items_1.RESOURCES.length
};
//# sourceMappingURL=index.js.map