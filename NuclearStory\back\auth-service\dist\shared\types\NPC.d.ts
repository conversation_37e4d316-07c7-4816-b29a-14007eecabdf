import { NPCType, FactionAlignment } from '../enums';
import { Position } from './Common';
import { Special } from '../models/Player';
import { InventoryItem } from '../models/InventoryItem';
export interface NPCDialogue {
    id: string;
    text: string;
    conditions?: string[];
    responses: NPCDialogueResponse[];
}
export interface NPCDialogueResponse {
    id: string;
    text: string;
    action?: 'trade' | 'quest' | 'info' | 'combat' | 'leave';
    requirements?: {
        items?: string[];
        skills?: Record<string, number>;
        reputation?: number;
        currency?: number;
    };
    consequences?: {
        addItems?: string[];
        removeItems?: string[];
        addQuest?: string;
        changeReputation?: number;
        giveCurrency?: number;
        takeCurrency?: number;
    };
}
export interface NPCTrade {
    buyPrices: Record<string, number>;
    sellItems: InventoryItem[];
    currency: number;
    tradeModifier: number;
}
export interface NPCSchedule {
    timeSlots: {
        startHour: number;
        endHour: number;
        locationId: string;
        activity: string;
    }[];
}
export interface NPC {
    id: string;
    name: string;
    description: string;
    type: NPCType;
    avatar?: string;
    appearance: {
        gender: 'male' | 'female' | 'other';
        age: number;
        height: number;
        build: 'thin' | 'average' | 'muscular' | 'heavy';
        hairColor: string;
        eyeColor: string;
        skinTone: string;
        distinguishingMarks?: string[];
    };
    special: Special;
    level: number;
    currentHP: number;
    maxHP: number;
    combatSkill: number;
    isHostile: boolean;
    position: Position;
    currentLocationId: string;
    homeLocationId?: string;
    movementSpeed: number;
    factionId?: string;
    factionRank?: string;
    alignment: FactionAlignment;
    reputation: number;
    inventory: InventoryItem[];
    equippedWeapon?: string;
    equippedArmor?: string;
    dialogues: NPCDialogue[];
    currentDialogueId?: string;
    hasSpokenToPlayer: boolean;
    lastInteractionAt?: Date;
    isTrader: boolean;
    trade?: NPCTrade;
    personality: {
        aggression: number;
        friendliness: number;
        intelligence: number;
        courage: number;
        greed: number;
    };
    schedule?: NPCSchedule;
    currentActivity: string;
    availableQuests: string[];
    completedQuests: string[];
    isAlive: boolean;
    isEssential: boolean;
    isUnique: boolean;
    createdAt: Date;
    lastSeenAt: Date;
    respawnAt?: Date;
}
