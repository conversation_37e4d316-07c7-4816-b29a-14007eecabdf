import { ContainerType } from '../enums';
import { Position } from '../models/Player';
export interface ContainerLock {
    type: 'key' | 'combination' | 'electronic' | 'biometric' | 'magical';
    difficulty: number;
    requirements: {
        keyId?: string;
        combination?: string;
        biometricId?: string;
        skillLevel?: number;
        toolRequired?: string;
    };
    isLocked: boolean;
    isBroken: boolean;
    isJammed: boolean;
    failedAttempts: number;
    maxAttempts: number;
    lockoutTime?: number;
}
export interface ContainerMaterial {
    type: 'wood' | 'metal' | 'plastic' | 'glass' | 'fabric' | 'leather' | 'composite';
    durability: number;
    maxDurability: number;
    resistances: {
        physical: number;
        fire: number;
        water: number;
        acid: number;
        radiation: number;
    };
    isTransparent: boolean;
    isWaterproof: boolean;
    isFireproof: boolean;
    isInsulated: boolean;
}
export interface ContainerProperties {
    dimensions: {
        width: number;
        height: number;
        depth: number;
    };
    weight: number;
    isPortable: boolean;
    requiresTwoHands: boolean;
    canBeOpened: boolean;
    canBeClosed: boolean;
    hasLid: boolean;
    preservesFood: boolean;
    preservesMedicine: boolean;
    protectsFromRadiation: boolean;
    maintainsTemperature: boolean;
    requiresElectricity: boolean;
    workingTemperatureRange?: {
        min: number;
        max: number;
    };
}
export interface Container {
    id: string;
    name: string;
    description: string;
    type: ContainerType;
    position: Position;
    rotation: number;
    isOpen: boolean;
    isDestroyed: boolean;
    isEmpty: boolean;
    material: ContainerMaterial;
    properties: ContainerProperties;
    lock?: ContainerLock;
    contents: string[];
    organization: {
        categories: Record<string, string[]>;
        quickAccess: string[];
        hiddenCompartments: Array<{
            id: string;
            name: string;
            isDiscovered: boolean;
            contents: string[];
            discoveryRequirement?: {
                skill: string;
                level: number;
            };
        }>;
    };
    access: {
        allowedFactions: string[];
        allowedNPCs: string[];
        allowedPlayers: string[];
        requiresPermission: boolean;
        permissionLevel: number;
        ownerId?: string;
        ownerType?: 'player' | 'npc' | 'faction';
    };
    interaction: {
        isInteractable: boolean;
        interactionRange: number;
        openTime: number;
        closeTime: number;
        searchTime: number;
        requirements?: {
            strength?: number;
            skill?: string;
            tool?: string;
        };
    };
    effects: {
        hasTrap?: boolean;
        trapType?: 'explosive' | 'poison' | 'electric' | 'alarm' | 'dart';
        trapDamage?: number;
        trapIsArmed?: boolean;
        trapIsTriggered?: boolean;
        onOpen?: {
            triggerAlarm?: boolean;
            alertNPCs?: string[];
            giveItems?: string[];
            removeItems?: string[];
            startQuest?: string;
        };
        onDestroy?: {
            scatterContents?: boolean;
            destroyContents?: boolean;
            createDebris?: boolean;
            triggerExplosion?: boolean;
        };
    };
    visual: {
        sprite?: string;
        model?: string;
        texture?: string;
        glowWhenLocked?: boolean;
        sparkWhenDamaged?: boolean;
        smokeWhenDestroyed?: boolean;
        hasStatusLight?: boolean;
        statusLightColor?: string;
        openAnimation?: string;
        closeAnimation?: string;
        searchAnimation?: string;
    };
    sounds: {
        openSound?: string;
        closeSound?: string;
        lockSound?: string;
        unlockSound?: string;
        breakSound?: string;
        searchSound?: string;
    };
    history: {
        timesOpened: number;
        timesLooted: number;
        timesLocked: number;
        timesBroken: number;
        lastOpenedAt?: Date;
        lastLootedAt?: Date;
        lastRepairedAt?: Date;
        lastInteractedBy?: {
            type: 'player' | 'npc';
            id: string;
            action: 'open' | 'close' | 'loot' | 'lock' | 'unlock' | 'break' | 'repair';
        };
    };
    respawn: {
        canRespawn: boolean;
        respawnTime: number;
        lastRespawnAt?: Date;
        respawnLootTable?: string;
        maxRespawns?: number;
        currentRespawns: number;
    };
    createdAt: Date;
    lastUpdatedAt: Date;
    version: string;
}
