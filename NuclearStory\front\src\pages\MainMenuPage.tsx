import React from 'react'
import { useNavigate } from 'react-router-dom'
import MainMenu from '../components/MainMenu'
import { useGameStore } from '../store/gameStore'

const MainMenuPage: React.FC = () => {
  const navigate = useNavigate()
  const { setCurrentWorld, setCurrentWorldId, worlds } = useGameStore()

  const handleStartGame = (worldId: string) => {
    // Находим мир по ID и устанавливаем его как текущий
    const world = worlds.find(w => w.id === worldId)
    if (world) {
      setCurrentWorld(world)
      setCurrentWorldId(worldId)
      navigate('/game')
    }
  }

  return <MainMenu onStartGame={handleStartGame} />
}

export default MainMenuPage
